{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=template&id=3cc1bfc8&scoped=true", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098310820}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754057743658}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}