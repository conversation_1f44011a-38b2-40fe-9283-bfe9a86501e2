{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=template&id=3cc1bfc8&scoped=true", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098438104}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754057743658}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}