{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098310820}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\babel.config.js", "mtime": 1754055039760}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754057742816}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_marked", "require", "_aigc", "_uuid", "_websocket", "_interopRequireDefault", "_store", "_turndown", "name", "data", "userId", "store", "state", "user", "id", "corpId", "corp_id", "chatId", "uuidv4", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "tyChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "method", "params", "aiList", "avatar", "capabilities", "label", "value", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "selectedCapability", "promptInput", "taskStarted", "autoPlay", "screenshots", "results", "activeResultTab", "activeCollapses", "showImageDialog", "currentLargeImage", "enabledAIs", "turndownService", "TurndownService", "headingStyle", "codeBlockStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreDialogVisible", "selectedResults", "scorePrompt", "layoutDialogVisible", "layoutPrompt", "currentLayoutResult", "historyDrawerVisible", "chatHistory", "pushOfficeNum", "pushingToWechat", "selectedMedia", "pushingToMedia", "tthFlowVisible", "tthFlowLogs", "tthFlowImages", "tthArticleEditVisible", "tthArticleTitle", "tthArticleContent", "computed", "canSend", "trim", "length", "some", "ai", "canScore", "canLayout", "groupedHistory", "_this", "groups", "chatGroups", "for<PERSON>ach", "item", "push", "Object", "values", "chatGroup", "sort", "a", "b", "Date", "createTime", "parentItem", "date", "getHistoryDate", "_objectSpread2", "default", "isParent", "children", "slice", "map", "child", "created", "console", "log", "initWebSocket", "loadChatHistory", "loadLastChat", "watch", "handler", "newMedia", "loadMediaPrompt", "immediate", "methods", "sendPrompt", "_this2", "filter", "$set", "includes", "message", "_this3", "then", "res", "code", "$message", "error", "messages", "selectSingleCapability", "capabilityValue", "$forceUpdate", "toggleCapability", "index", "indexOf", "newCapabilities", "_toConsumableArray2", "splice", "getStatusText", "getStatusIcon", "renderMarkdown", "text", "marked", "htmlToText", "html", "tempDiv", "document", "createElement", "innerHTML", "textContent", "innerText", "htmlToMarkdown", "turndown", "copyResult", "content", "plainText", "textarea", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "success", "exportResult", "result", "markdown", "blob", "Blob", "type", "link", "href", "URL", "createObjectURL", "download", "concat", "aiName", "toISOString", "click", "revokeObjectURL", "openShareUrl", "shareUrl", "window", "open", "warning", "showLargeImage", "imageUrl", "_this4", "currentIndex", "$nextTick", "carousel", "$el", "querySelector", "__vue__", "setActiveItem", "closeLargeImage", "handleImageError", "event", "target", "src", "handleImageLoad", "_this5", "wsUrl", "process", "env", "VUE_APP_WS_API", "websocketClient", "connect", "handleWebSocketMessage", "datastr", "dataObj", "JSON", "parse", "maxChatId", "targetAI", "find", "existingLog", "unshift", "timestamp", "isCompleted", "zhihuAI", "baijiahaoAI", "url", "warn", "wkpfAI", "draftContent", "shareImgUrl", "saveHistory", "znpbAI", "pushToWechatWithContent", "tthpbAI", "title", "resultIndex", "findIndex", "r", "closeWebSocket", "close", "sendMessage", "_this6", "send", "scrollToBottom", "toggleAIExpansion", "formatTime", "toLocaleTimeString", "hour", "minute", "second", "hour12", "showScoreDialog", "handleScore", "_this7", "selectedContents", "plainContent", "join", "fullPrompt", "scoreRequest", "existIndex", "wkpf", "showHistoryDrawer", "handleHistoryDrawerClose", "isAll", "_this8", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "p", "n", "getChatHistory", "v", "formatHistoryTime", "today", "yesterday", "setDate", "getDate", "twoDaysAgo", "threeDaysAgo", "toDateString", "toLocaleDateString", "year", "month", "day", "loadHistoryItem", "historyData", "_this9", "_callee2", "_t2", "_context2", "saveUserChatData", "stringify", "toggleHistoryExpansion", "createNewChat", "_this0", "_callee3", "lastChat", "_t3", "_context3", "isImageFile", "imageExtensions", "url<PERSON><PERSON><PERSON>", "toLowerCase", "ext", "isPdfFile", "getImageStyle", "widthMap", "DeepSeek", "豆包", "通义千问", "width", "height", "handlePushToMedia", "showLayoutDialog", "media", "_this1", "_callee4", "platformId", "response", "_t4", "_context4", "getMediaCallWord", "getDefaultPrompt", "handleLayout", "createZhihuDeliveryTask", "createToutiaoLayoutTask", "createBaijiahaoLayoutTask", "createWechatLayoutTask", "zhihu", "zhihuRequest", "contentText", "b<PERSON><PERSON><PERSON><PERSON>", "baijiahaoRequest", "layoutRequest", "znpb", "scoreResult", "scoreContent", "tthpb", "_this10", "num", "pushAutoOffice", "msg", "catch", "finally", "confirmTTHPublish", "publishRequest", "closeTTHFlowDialog"], "sources": ["src/views/wechat/chrome/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\" />\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img\r\n              :src=\"require('../../../assets/ai/celan.png')\"\r\n              alt=\"历史记录\"\r\n              class=\"history-icon\"\r\n            />\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer\r\n      title=\"历史会话记录\"\r\n      :visible.sync=\"historyDrawerVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\"\r\n    >\r\n      <div class=\"history-content\">\r\n        <div\r\n          v-for=\"(group, date) in groupedHistory\"\r\n          :key=\"date\"\r\n          class=\"history-group\"\r\n        >\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div\r\n              v-for=\"(item, index) in group\"\r\n              :key=\"index\"\r\n              class=\"history-item\"\r\n            >\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i\r\n                    :class=\"[\r\n                      'el-icon-arrow-right',\r\n                      { 'is-expanded': item.isExpanded },\r\n                    ]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"\r\n                  ></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">\r\n                  {{ formatHistoryTime(item.createTime) }}\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-if=\"\r\n                  item.children && item.children.length > 0 && item.isExpanded\r\n                \"\r\n                class=\"history-children\"\r\n              >\r\n                <div\r\n                  v-for=\"(child, childIndex) in item.children\"\r\n                  :key=\"childIndex\"\r\n                  class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\"\r\n                >\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">\r\n                    {{ formatHistoryTime(child.createTime) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card\r\n                v-for=\"(ai, index) in aiList\"\r\n                :key=\"index\"\r\n                class=\"ai-card\"\r\n                shadow=\"hover\"\r\n              >\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\" />\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch\r\n                      v-model=\"ai.enabled\"\r\n                      active-color=\"#13ce66\"\r\n                      inactive-color=\"#ff4949\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <!-- 通义只支持单选-->\r\n                  <div v-if=\"ai.name === '通义千问'\" class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapability === capability.value ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"ai.selectedCapability !== capability.value\"\r\n                      @click=\"selectSingleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                  <!-- 其他AI -->\r\n                  <div v-else class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\"\r\n                      size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入提示词，支持Markdown格式\"\r\n              v-model=\"promptInput\"\r\n              resize=\"none\"\r\n              class=\"prompt-input\"\r\n            >\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"sendPrompt\"\r\n                :disabled=\"!canSend\"\r\n                class=\"send-button\"\r\n              >\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div\r\n                  v-for=\"(ai, index) in enabledAIs\"\r\n                  :key=\"index\"\r\n                  class=\"task-item\"\r\n                >\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i\r\n                        :class=\"[\r\n                          'el-icon-arrow-right',\r\n                          { 'is-expanded': ai.isExpanded },\r\n                        ]\"\r\n                      ></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{\r\n                        getStatusText(ai.status)\r\n                      }}</span>\r\n                      <i\r\n                        :class=\"getStatusIcon(ai.status)\"\r\n                        class=\"status-icon\"\r\n                      ></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div\r\n                    class=\"progress-timeline\"\r\n                    v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\"\r\n                  >\r\n                    <div class=\"timeline-scroll\">\r\n                      <div\r\n                        v-for=\"(log, logIndex) in ai.progressLogs\"\r\n                        :key=\"logIndex\"\r\n                        class=\"progress-item\"\r\n                        :class=\"{\r\n                          completed: log.isCompleted || logIndex > 0,\r\n                          current: !log.isCompleted && logIndex === 0,\r\n                        }\"\r\n                      >\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div\r\n                          class=\"progress-line\"\r\n                          v-if=\"logIndex < ai.progressLogs.length - 1\"\r\n                        ></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">\r\n                            {{ formatTime(log.timestamp) }}\r\n                          </div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch\r\n                    v-model=\"autoPlay\"\r\n                    active-text=\"自动轮播\"\r\n                    inactive-text=\"手动切换\"\r\n                  >\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel\r\n                  :interval=\"3000\"\r\n                  :autoplay=\"false\"\r\n                  indicator-position=\"outside\"\r\n                  height=\"700px\"\r\n                >\r\n                  <el-carousel-item\r\n                    v-for=\"(screenshot, index) in screenshots\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <img\r\n                      v-if=\"screenshot && screenshot.trim() !== ''\"\r\n                      :src=\"screenshot\"\r\n                      alt=\"执行截图\"\r\n                      class=\"screenshot-image\"\r\n                      @click=\"showLargeImage(screenshot)\"\r\n                      @error=\"handleImageError\"\r\n                      @load=\"handleImageLoad\"\r\n                    />\r\n                    <div v-else class=\"screenshot-error\">截图地址无效</div>\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane\r\n            v-for=\"(result, index) in results\"\r\n            :key=\"index\"\r\n            :label=\"result.aiName\"\r\n            :name=\"'result-' + index\"\r\n          >\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-link\"\r\n                    @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\"\r\n                  >\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"success\"\r\n                    icon=\"el-icon-s-promotion\"\r\n                    @click=\"handlePushToMedia(result)\"\r\n                    class=\"push-media-btn\"\r\n                    :loading=\"pushingToMedia\"\r\n                    :disabled=\"pushingToMedia\"\r\n                  >\r\n                    投递到媒体\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl && result.shareImgUrl.trim() !== ''\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img\r\n                  v-if=\"isImageFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  alt=\"分享图片\"\r\n                  class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\"\r\n                  @error=\"handleImageError\"\r\n                  @load=\"handleImageLoad\"\r\n                />\r\n                <!-- 渲染PDF -->\r\n                <iframe\r\n                  v-else-if=\"isPdfFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  class=\"share-pdf\"\r\n                  frameborder=\"0\"\r\n                >\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"openShareUrl(result.shareImgUrl)\"\r\n                  >\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"markdown-content\"\r\n                v-html=\"renderMarkdown(result.content)\"\r\n              ></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  @click=\"copyResult(result.content)\"\r\n                  >复制（纯文本）</el-button\r\n                >\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"success\"\r\n                  @click=\"exportResult(result)\"\r\n                  >导出（MD文件）</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"showImageDialog\"\r\n      width=\"90%\"\r\n      :show-close=\"true\"\r\n      :modal=\"true\"\r\n      center\r\n      class=\"image-dialog\"\r\n      :append-to-body=\"true\"\r\n      @close=\"closeLargeImage\"\r\n    >\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div\r\n          v-if=\"currentLargeImage && currentLargeImage.trim() !== '' && !screenshots.includes(currentLargeImage)\"\r\n          class=\"single-image-container\"\r\n        >\r\n          <img\r\n            :src=\"currentLargeImage\"\r\n            alt=\"大图\"\r\n            class=\"large-image\"\r\n            @error=\"handleImageError\"\r\n            @load=\"handleImageLoad\"\r\n          />\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel\r\n          v-else-if=\"screenshots && screenshots.length > 0\"\r\n          :interval=\"3000\"\r\n          :autoplay=\"false\"\r\n          indicator-position=\"outside\"\r\n          height=\"80vh\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(screenshot, index) in screenshots\"\r\n            :key=\"index\"\r\n          >\r\n            <img\r\n              v-if=\"screenshot && screenshot.trim() !== ''\"\r\n              :src=\"screenshot\"\r\n              alt=\"大图\"\r\n              class=\"large-image\"\r\n              @error=\"handleImageError\"\r\n              @load=\"handleImageLoad\"\r\n            />\r\n            <div v-else class=\"image-error\">图片地址无效</div>\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n        <!-- 没有有效图片时显示提示 -->\r\n        <div v-else class=\"no-image-tip\">\r\n          <i class=\"el-icon-picture\"></i>\r\n          <p>暂无图片可显示</p>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog\r\n      title=\"智能评分\"\r\n      :visible.sync=\"scoreDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\"\r\n    >\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"10\"\r\n            placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\"\r\n            v-model=\"scorePrompt\"\r\n            resize=\"none\"\r\n            class=\"score-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox\r\n              v-for=\"(result, index) in results\"\r\n              :key=\"index\"\r\n              :label=\"result.aiName\"\r\n              class=\"result-checkbox\"\r\n            >\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 投递到媒体弹窗 -->\r\n    <el-dialog\r\n      title=\"媒体投递设置\"\r\n      :visible.sync=\"layoutDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"layout-dialog\"\r\n    >\r\n      <div class=\"layout-dialog-content\">\r\n        <!-- 媒体选择区域 -->\r\n        <div class=\"media-selection-section\">\r\n          <h3>选择投递媒体：</h3>\r\n          <el-radio-group v-model=\"selectedMedia\" size=\"small\" class=\"media-radio-group\">\r\n            <el-radio-button label=\"wechat\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              公众号\r\n            </el-radio-button>\r\n            <el-radio-button label=\"zhihu\">\r\n              <i class=\"el-icon-document\"></i>\r\n              知乎\r\n            </el-radio-button>\r\n            <el-radio-button label=\"toutiao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              微头条\r\n            </el-radio-button>\r\n            <el-radio-button label=\"baijiahao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              百家号\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n          <div class=\"media-description\">\r\n            <template v-if=\"selectedMedia === 'wechat'\">\r\n              <small>📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'zhihu'\">\r\n              <small>📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>📰 将内容转换为微头条文章格式，支持文章编辑和发布</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>🔈 将内容转换为百家号帖子格式，直接投递到百家号草稿箱</small>\r\n            </template>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"layout-prompt-section\">\r\n          <h3>排版提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"12\"\r\n            placeholder=\"请输入排版提示词\"\r\n            v-model=\"layoutPrompt\"\r\n            resize=\"none\"\r\n            class=\"layout-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"layoutDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleLayout\" :disabled=\"!canLayout\">\r\n          排版后智能投递\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条发布流程弹窗 -->\r\n    <el-dialog title=\"微头条发布流程\" :visible.sync=\"tthFlowVisible\" width=\"60%\" height=\"60%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-flow-dialog\">\r\n      <div class=\"tth-flow-content\">\r\n        <div class=\"flow-logs-section\">\r\n          <h3>发布流程日志：</h3>\r\n          <div class=\"progress-timeline\">\r\n            <div class=\"timeline-scroll\">\r\n              <div v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" class=\"progress-item completed\">\r\n                <div class=\"progress-dot\"></div>\r\n                <div v-if=\"index < tthFlowLogs.length - 1\" class=\"progress-line\"></div>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                  <div class=\"progress-text\">{{ log.content }}</div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"tthFlowLogs.length === 0\" class=\"no-logs\">暂无流程日志...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flow-images-section\">\r\n          <h3>发布流程图片：</h3>\r\n          <div class=\"flow-images-container\">\r\n            <template v-if=\"tthFlowImages.length > 0\">\r\n              <div v-for=\"(image, index) in tthFlowImages\" :key=\"index\" class=\"flow-image-item\">\r\n                <img\r\n                  v-if=\"image && image.trim() !== ''\"\r\n                  :src=\"image\"\r\n                  alt=\"流程图片\"\r\n                  class=\"flow-image\"\r\n                  @click=\"showLargeImage(image)\"\r\n                  @error=\"handleImageError\"\r\n                  @load=\"handleImageLoad\"\r\n                >\r\n                <div v-else class=\"image-error\">图片地址无效</div>\r\n              </div>\r\n            </template>\r\n            <div v-else class=\"no-logs\">暂无流程图片...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeTTHFlowDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条文章编辑弹窗 -->\r\n    <el-dialog title=\"微头条文章编辑\" :visible.sync=\"tthArticleEditVisible\" width=\"70%\" height=\"80%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-article-edit-dialog\">\r\n      <div class=\"tth-article-edit-content\">\r\n        <div class=\"article-title-section\">\r\n          <h3>文章标题：</h3>\r\n          <el-input v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" class=\"article-title-input\"></el-input>\r\n        </div>\r\n        <div class=\"article-content-section\">\r\n          <h3>文章内容：</h3>\r\n          <div class=\"content-input-wrapper\">\r\n            <el-input \r\n              type=\"textarea\" \r\n              v-model=\"tthArticleContent\" \r\n              :rows=\"20\" \r\n              placeholder=\"请输入文章内容\"\r\n              resize=\"none\" \r\n              class=\"article-content-input\"\r\n              :class=\"{ 'content-over-limit': tthArticleContent.length > 2000 }\"\r\n            ></el-input>\r\n            <div class=\"content-length-info\" :class=\"{ 'text-danger': tthArticleContent.length > 2000 }\">\r\n              字数：{{ tthArticleContent.length }}/2000\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"tthArticleEditVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmTTHPublish\" :disabled=\"!tthArticleTitle || !tthArticleContent\">\r\n          确定发布\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { marked } from \"marked\";\r\nimport {\r\n  message,\r\n  saveUserChatData,\r\n  getChatHistory,\r\n  pushAutoOffice,\r\n  getMediaCallWord,\r\n} from \"@/api/wechat/aigc\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport websocketClient from \"@/utils/websocket\";\r\nimport store from \"@/store\";\r\nimport TurndownService from \"turndown\";\r\n\r\nexport default {\r\n  name: \"AIManagementPlatform\",\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: \"\",\r\n        userId: \"\",\r\n        corpId: \"\",\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        isNewChat: true,\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"\",\r\n        params: {},\r\n      },\r\n      aiList: [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: \"\",\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: \"result-0\",\r\n      activeCollapses: [\"ai-selection\", \"prompt-input\"], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: \"\",\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: \"atx\",\r\n        codeBlockStyle: \"fenced\",\r\n        emDelimiter: \"*\",\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇内容，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      layoutDialogVisible: false,\r\n      layoutPrompt: \"\",\r\n      currentLayoutResult: null, // 当前要排版的结果\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n      selectedMedia: \"wechat\", // 默认选择公众号\r\n      pushingToMedia: false, // 投递到媒体的loading状态\r\n      // 微头条相关变量\r\n      tthFlowVisible: false, // 微头条发布流程弹窗\r\n      tthFlowLogs: [], // 微头条发布流程日志\r\n      tthFlowImages: [], // 微头条发布流程图片\r\n      tthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n      tthArticleTitle: '', // 微头条文章标题\r\n      tthArticleContent: '', // 微头条文章内容\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return (\r\n        this.promptInput.trim().length > 0 &&\r\n        this.aiList.some((ai) => ai.enabled)\r\n      );\r\n    },\r\n    canScore() {\r\n      return (\r\n        this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0\r\n      );\r\n    },\r\n    canLayout() {\r\n      return this.layoutPrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach((item) => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach((chatGroup) => {\r\n        // 按时间排序\r\n        chatGroup.sort(\r\n          (a, b) => new Date(a.createTime) - new Date(b.createTime)\r\n        );\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map((child) => ({\r\n            ...child,\r\n            isParent: false,\r\n          })),\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    },\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  watch: {\r\n    // 监听媒体选择变化，自动加载对应的提示词\r\n    selectedMedia: {\r\n      handler(newMedia) {\r\n        this.loadMediaPrompt(newMedia);\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots = [];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = \"\";\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter((ai) => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach((ai) => {\r\n        this.$set(ai, \"status\", \"running\");\r\n      });\r\n\r\n      this.enabledAIs.forEach((ai) => {\r\n        if (ai.name === \"DeepSeek\" && ai.enabled) {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"deepseek,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-lwss,\";\r\n          }\r\n        }\r\n        if (ai.name === \"豆包\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db-sdsk,\";\r\n          }\r\n        }\r\n        if (ai.name === \"MiniMax Chat\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n          }\r\n        }\r\n        if(ai.name === '通义千问' && ai.enabled){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n          if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n          } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq);\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\";\r\n      this.jsonRpcReqest.params = this.userInfoReq;\r\n      this.message(this.jsonRpcReqest);\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then((res) => {\r\n        if (res.code == 201) {\r\n          this.$message.error(res.messages || '操作失败');\r\n        }\r\n      });\r\n    },\r\n    // 处理通义单选逻辑\r\n    selectSingleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      if (ai.selectedCapability === capabilityValue) {\r\n        this.$set(ai, 'selectedCapability', '');\r\n      } else {\r\n        this.$set(ai, 'selectedCapability', capabilityValue);\r\n      }\r\n      this.$forceUpdate();\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log(\"切换前:\", ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(\r\n          ai.selectedCapabilities,\r\n          ai.selectedCapabilities.length,\r\n          capabilityValue\r\n        );\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, \"selectedCapabilities\", newCapabilities);\r\n      }\r\n      console.log(\"切换后:\", ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"等待中\";\r\n        case \"running\":\r\n          return \"正在执行\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"failed\":\r\n          return \"执行失败\";\r\n        default:\r\n          return \"未知状态\";\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"el-icon-time\";\r\n        case \"running\":\r\n          return \"el-icon-loading\";\r\n        case \"completed\":\r\n          return \"el-icon-check success-icon\";\r\n        case \"failed\":\r\n          return \"el-icon-close error-icon\";\r\n        default:\r\n          return \"el-icon-question\";\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement(\"div\");\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || \"\";\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement(\"textarea\");\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textarea);\r\n      this.$message.success(\"已复制纯文本到剪贴板\");\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: \"text/markdown\" });\r\n      const link = document.createElement(\"a\");\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date()\r\n        .toISOString()\r\n        .slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success(\"已导出Markdown文件\");\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, \"_blank\");\r\n      } else {\r\n        this.$message.warning(\"暂无原链接\");\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      // 检查图片URL是否有效\r\n      if (!imageUrl || imageUrl.trim() === '') {\r\n        this.$message.warning(\"图片地址无效\");\r\n        return;\r\n      }\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector(\".image-dialog .el-carousel\");\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = \"\";\r\n    },\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error(\"图片加载失败:\", event.target.src);\r\n      this.$message.error(\"图片加载失败，请检查图片地址是否正确\");\r\n    },\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log(\"图片加载成功:\", event.target.src);\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log(\"WebSocket URL:\", process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case \"open\":\r\n            // this.$message.success('');\r\n            break;\r\n          case \"message\":\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case \"close\":\r\n            this.$message.warning(\"WebSocket连接已关闭\");\r\n            break;\r\n          case \"error\":\r\n            this.$message.error(\"WebSocket连接错误\");\r\n            break;\r\n          case \"reconnect_failed\":\r\n            this.$message.error(\"WebSocket重连失败，请刷新页面重试\");\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === \"RETURN_YBT1_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_YBDS_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_DB_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.tyChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.maxChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_LOG\" && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(\r\n          (ai) => ai.name === dataObj.aiName\r\n        );\r\n        if (targetAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = targetAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            targetAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到知乎\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            zhihuAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到百家号\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            baijiahaoAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理截图消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_IMG\" && dataObj.url) {\r\n        // 验证URL有效性后再添加到数组\r\n        if (dataObj.url.trim() !== '') {\r\n          this.screenshots.unshift(dataObj.url);\r\n          console.log(\"收到新截图:\", dataObj.url);\r\n        } else {\r\n          console.warn(\"收到无效的截图URL:\", dataObj.url);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能评分结果\r\n      if (dataObj.type === \"RETURN_WKPF_RES\") {\r\n        const wkpfAI = this.enabledAIs.find((ai) => ai.name === \"智能评分\");\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, \"status\", \"completed\");\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: \"智能评分\",\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能排版结果\r\n      if (dataObj.type === \"RETURN_ZNPB_RES\") {\r\n        const znpbAI = this.enabledAIs.find((ai) => ai.name === \"智能排版\");\r\n        if (znpbAI) {\r\n          this.$set(znpbAI, \"status\", \"completed\");\r\n          if (znpbAI.progressLogs.length > 0) {\r\n            this.$set(znpbAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 直接调用投递到公众号的方法，不添加到结果展示\r\n          this.pushToWechatWithContent(dataObj.draftContent);\r\n\r\n          // 智能排版完成时，保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_ZHIHU_DELIVERY_RES\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          this.$set(zhihuAI, \"status\", \"completed\");\r\n          if (zhihuAI.progressLogs.length > 0) {\r\n            this.$set(zhihuAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          zhihuAI.progressLogs.unshift({\r\n            content: \"知乎投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 知乎投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"知乎投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_BAIJIAHAO_DELIVERY_RES\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          this.$set(baijiahaoAI, \"status\", \"completed\");\r\n          if (baijiahaoAI.progressLogs.length > 0) {\r\n            this.$set(baijiahaoAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          baijiahaoAI.progressLogs.unshift({\r\n            content: \"百家号投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 百家号投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"百家号投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理微头条排版结果\r\n      if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n        // 微头条排版AI节点状态设为已完成\r\n        const tthpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n        if (tthpbAI) {\r\n          this.$set(tthpbAI, 'status', 'completed');\r\n          if (tthpbAI.progressLogs.length > 0) {\r\n            this.$set(tthpbAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n        }\r\n        this.tthArticleTitle = dataObj.title || '';\r\n        this.tthArticleContent = dataObj.content || '';\r\n        this.tthArticleEditVisible = true;\r\n        this.saveHistory();\r\n        return;\r\n      }\r\n\r\n      // 处理微头条发布流程\r\n      if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n        // 添加流程日志\r\n        if (dataObj.content) {\r\n          this.tthFlowLogs.push({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            type: 'flow',\r\n          });\r\n        }\r\n        // 处理图片信息\r\n        if (dataObj.shareImgUrl) {\r\n          this.tthFlowImages.push(dataObj.shareImgUrl);\r\n        }\r\n        // 确保流程弹窗显示\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        // 检查发布结果\r\n        if (dataObj.content === 'success') {\r\n          this.$message.success('发布到微头条成功！');\r\n          this.tthFlowVisible = true;\r\n        } else if (dataObj.content === 'false' || dataObj.content === false) {\r\n          this.$message.error('发布到微头条失败！');\r\n          this.tthFlowVisible = false;\r\n          this.tthArticleEditVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 兼容后端发送的RETURN_PC_TTH_IMG类型图片消息\r\n      if (dataObj.type === 'RETURN_PC_TTH_IMG' && dataObj.url) {\r\n        this.tthFlowImages.push(dataObj.url);\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case \"RETURN_YBT1_RES\":\r\n        case \"RETURN_TURBOS_RES\":\r\n        case \"RETURN_TURBOS_LARGE_RES\":\r\n        case \"RETURN_DEEPSEEK_RES\":\r\n          console.log(\"收到DeepSeek消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"DeepSeek\");\r\n          break;\r\n        case \"RETURN_YBDS_RES\":\r\n        case \"RETURN_DB_RES\":\r\n          console.log(\"收到豆包消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"豆包\");\r\n          break;\r\n        case \"RETURN_MAX_RES\":\r\n          console.log(\"收到MiniMax消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n          break;\r\n        case 'RETURN_TY_RES':\r\n          console.log('收到通义千问消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n          break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, \"status\", \"completed\");\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], \"isCompleted\", true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(\r\n          (r) => r.aiName === targetAI.name\r\n        );\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error(\"WebSocket未连接\");\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, \"isExpanded\", !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter((result) => this.selectedResults.includes(result.aiName))\r\n        .map((result) => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join(\"\\n\");\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"AI评分\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: \"zj-db-sdsk\", // 默认使用豆包进行评分\r\n        },\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest);\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: \"智能评分\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"智能评分任务已提交，正在评分...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"智能评分\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"智能评分\"\r\n      );\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"评分请求已发送，请等待结果\");\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return \"今天\";\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return \"昨天\";\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return \"两天前\";\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return \"三天前\";\r\n      } else {\r\n        return date.toLocaleDateString(\"zh-CN\", {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || \"\";\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || \"\";\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || \"\";\r\n        this.userInfoReq.dbChatId = item.dbChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.tyChatId || \"\";\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success(\"历史记录加载成功\");\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId,\r\n        tyChatId: this.userInfoReq.tyChatId,\r\n        maxChatId: this.userInfoReq.maxChatId,\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n          maxChatId: this.userInfoReq.maxChatId,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"保存历史记录失败:\", error);\r\n        this.$message.error(\"保存历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(\r\n        this.expandedHistoryItems,\r\n        item.chatId,\r\n        !this.expandedHistoryItems[item.chatId]\r\n      );\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = \"\";\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: \"\",\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        maxChatId: \"\",\r\n        isNewChat: true,\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n\r\n      this.$message.success(\"已创建新对话\");\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId, 0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载上次会话失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = [\r\n        \".jpg\",\r\n        \".jpeg\",\r\n        \".png\",\r\n        \".gif\",\r\n        \".bmp\",\r\n        \".webp\",\r\n        \".svg\",\r\n      ];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some((ext) => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes(\".pdf\");\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        DeepSeek: \"700px\",\r\n        豆包: \"560px\",\r\n        通义千问: \"700px\",\r\n      };\r\n\r\n      const width = widthMap[aiName] || \"560px\"; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: \"auto\",\r\n      };\r\n    },\r\n\r\n    // 投递到媒体\r\n    handlePushToMedia(result) {\r\n      this.currentLayoutResult = result;\r\n      this.showLayoutDialog(result);\r\n    },\r\n\r\n    // 显示智能排版对话框\r\n    showLayoutDialog(result) {\r\n      this.currentLayoutResult = result;\r\n      this.layoutDialogVisible = true;\r\n      // 加载当前选择媒体的提示词\r\n      this.loadMediaPrompt(this.selectedMedia);\r\n    },\r\n\r\n    // 加载媒体提示词\r\n    async loadMediaPrompt(media) {\r\n      if (!media) return;\r\n\r\n      let platformId;\r\n      if(media === 'wechat'){\r\n        platformId = 'wechat_layout';\r\n      }else if(media === 'zhihu'){\r\n        platformId = 'zhihu_layout';\r\n      }else if(media === 'baijiahao'){\r\n        platformId = 'baijiahao_layout';\r\n      }else if(media === 'toutiao'){\r\n        platformId = 'weitoutiao_layout';\r\n      }\r\n\r\n      try {\r\n        const response = await getMediaCallWord(platformId);\r\n        if (response.code === 200) {\r\n          this.layoutPrompt = response.data + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        } else {\r\n          // 使用默认提示词\r\n          this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载提示词失败:', error);\r\n        // 使用默认提示词\r\n        this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n      }\r\n    },\r\n\r\n    // 获取默认提示词(仅在后端访问失败时使用)\r\n    getDefaultPrompt(media) {\r\n      if (media === 'wechat') {\r\n        return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：`;\r\n      } else if (media === 'zhihu') {\r\n        return `请将以下内容整理为适合知乎发布的Markdown格式文章。要求：\r\n1. 保持内容的专业性和可读性\r\n2. 使用合适的标题层级（## ### #### 等）\r\n3. 代码块使用\\`\\`\\`标记，并指定语言类型\r\n4. 重要信息使用**加粗**标记\r\n5. 列表使用- 或1. 格式\r\n6. 删除不必要的格式标记\r\n7. 确保内容适合知乎的阅读习惯\r\n8. 文章结构清晰，逻辑连贯\r\n9. 目标是作为一篇专业文章投递到知乎草稿箱\r\n\r\n请对以下内容进行排版：`;\r\n\r\n      }else if (media === 'baijiahao') {\r\n        return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括\"一、\"，\"1.\"等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话`;\r\n\r\n      }else if (media === 'toutiao') {\r\n        return `根据智能评分内容，写一篇微头条文章，只能包含标题和内容，要求如下：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数严格控制在1000字以上，2000字以下\r\n7. 强制要求：只能回答标题和内容，标题必须用英文双引号（\"\"）引用起来，且放在首位，不能有其他多余的话\r\n8. 严格要求：AI必须严格遵守所有严格条件，不要输出其他多余的内容，只要标题和内容\r\n9. 内容不允许出现编号，要正常文章格式\r\n\r\n请对以下内容进行排版：`;\r\n      }\r\n      return '请对以下内容进行排版：';\r\n    },\r\n\r\n    // 处理智能排版\r\n    handleLayout() {\r\n      if (!this.canLayout || !this.currentLayoutResult) return;\r\n      this.layoutDialogVisible = false;\r\n\r\n      if (this.selectedMedia === 'zhihu') {\r\n        // 知乎投递：直接创建投递任务\r\n        this.createZhihuDeliveryTask();\r\n      } else if (this.selectedMedia === 'toutiao') {\r\n        // 微头条投递：创建微头条排版任务\r\n        this.createToutiaoLayoutTask();\r\n      } else if (this.selectedMedia === 'baijiahao') {\r\n        // 百家号投递：创建百家号排版任务\r\n        this.createBaijiahaoLayoutTask();\r\n      }else {\r\n        // 公众号投递：创建排版任务\r\n        this.createWechatLayoutTask();\r\n      }\r\n    },\r\n// 创建知乎投递任务（独立任务）\r\n    createZhihuDeliveryTask() {\r\n      const zhihuAI = {\r\n        name: \"投递到知乎\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"知乎投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到知乎\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在知乎投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到知乎\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(zhihuAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = zhihuAI;\r\n        const zhihu = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(zhihu);\r\n      }\r\n\r\n      // 发送知乎投递请求\r\n      const zhihuRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到知乎\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"zhihu\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"知乎投递参数\", zhihuRequest);\r\n      this.message(zhihuRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"知乎投递任务已创建，正在处理...\");\r\n    },\r\n    // 创建百家号投递任务（独立任务）\r\n    createBaijiahaoLayoutTask() {\r\n      const baijiahaoAI = {\r\n        name: \"投递到百家号\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"百家号投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到百家号\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在百家号投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到百家号\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = baijiahaoAI;\r\n        const baijiahao = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      }\r\n\r\n      // 发送百家号投递请求\r\n      const baijiahaoRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到百家号\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"baijiahao\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"百家号投递参数\", baijiahaoRequest);\r\n      this.message(baijiahaoRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"百家号投递任务已创建，正在处理...\");\r\n    },\r\n      // 创建公众号排版任务（保持原有逻辑）\r\n      createWechatLayoutTask() {\r\n        const layoutRequest = {\r\n          jsonrpc: \"2.0\",\r\n          id: uuidv4(),\r\n          method: \"AI排版\",\r\n          params: {\r\n            taskId: uuidv4(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: this.layoutPrompt,\r\n            roles: \"\",\r\n            selectedMedia: \"wechat\",\r\n          },\r\n        };\r\n\r\n        console.log(\"公众号排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        const znpbAI = {\r\n          name: \"智能排版\",\r\n          avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"running\",\r\n          progressLogs: [\r\n            {\r\n              content: \"智能排版任务已提交，正在排版...\",\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: \"智能排版\",\r\n            },\r\n          ],\r\n          isExpanded: true,\r\n        };\r\n\r\n        // 检查是否已存在智能排版任务\r\n        const existIndex = this.enabledAIs.findIndex(\r\n          (ai) => ai.name === \"智能排版\"\r\n        );\r\n        if (existIndex === -1) {\r\n          this.enabledAIs.unshift(znpbAI);\r\n        } else {\r\n          this.enabledAIs[existIndex] = znpbAI;\r\n          const znpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n          this.enabledAIs.unshift(znpb);\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.$message.success(\"排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 创建微头条排版任务\r\n    createToutiaoLayoutTask() {\r\n      // 获取智能评分内容\r\n      const scoreResult = this.results.find(r => r.aiName === '智能评分');\r\n      const scoreContent = scoreResult ? scoreResult.content : '';\r\n\r\n      const layoutRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"微头条排版\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: `${scoreContent}\\n${this.layoutPrompt}`,\r\n          roles: \"\",\r\n        },\r\n      };\r\n\r\n      console.log(\"微头条排版参数\", layoutRequest);\r\n      this.message(layoutRequest);\r\n\r\n      const tthpbAI = {\r\n        name: \"微头条排版\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"微头条排版任务已提交，正在排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"微头条排版\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在微头条排版任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"微头条排版\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(tthpbAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = tthpbAI;\r\n        const tthpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(tthpb);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"微头条排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 实际投递到公众号\r\n    pushToWechatWithContent(contentText) {\r\n      if (this.pushingToWechat) return;\r\n      this.$message.success(\"开始投递公众号！\");\r\n      this.pushingToWechat = true;\r\n      this.pushOfficeNum += 1;\r\n\r\n      const params = {\r\n        contentText: contentText,\r\n        shareUrl: this.currentLayoutResult.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: this.currentLayoutResult.aiName,\r\n      };\r\n\r\n      pushAutoOffice(params)\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.$message.success(\"投递到公众号成功！\");\r\n          } else {\r\n            this.$message.error(res.msg || \"投递失败，请重试\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"投递到公众号失败:\", error);\r\n          this.$message.error(\"投递失败，请重试\");\r\n        })\r\n        .finally(() => {\r\n          this.pushingToWechat = false;\r\n        });\r\n    },\r\n\r\n\r\n\r\n    // 确认微头条发布\r\n    confirmTTHPublish() {\r\n      if (!this.tthArticleTitle || !this.tthArticleContent) {\r\n        this.$message.warning('请填写标题和内容');\r\n        return;\r\n      }\r\n      // 构建微头条发布请求\r\n      const publishRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n                  method: '微头条发布',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          roles: '',\r\n          title: this.tthArticleTitle,\r\n          content: this.tthArticleContent,\r\n          type: '微头条发布'\r\n        }\r\n      };\r\n      // 发送发布请求\r\n      console.log(\"微头条发布参数\", publishRequest);\r\n      this.message(publishRequest);\r\n      this.tthArticleEditVisible = false;\r\n      // 显示流程弹窗\r\n      this.tthFlowVisible = true;\r\n      this.tthFlowLogs = [];\r\n      this.tthFlowImages = [];\r\n      this.$message.success('微头条发布请求已发送！');\r\n    },\r\n\r\n\r\n    // 关闭微头条发布流程弹窗\r\n    closeTTHFlowDialog() {\r\n      this.tthFlowVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card,\r\n.screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.screenshot-error {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  color: #f56c6c;\r\n  font-size: 16px;\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn,\r\n.push-media-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n  min-height: 80vh;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-error {\r\n  color: #f56c6c;\r\n  font-size: 16px;\r\n  text-align: center;\r\n  padding: 20px;\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border-radius: 4px;\r\n  margin: 10px;\r\n}\r\n\r\n.no-image-tip {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #909399;\r\n  font-size: 16px;\r\n  height: 80vh;\r\n}\r\n\r\n.no-image-tip i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #c0c4cc;\r\n}\r\n\r\n.no-image-tip p {\r\n  margin: 0;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.layout-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.layout-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.layout-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.layout-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.layout-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.layout-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409eff;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 用于处理DeepSeek特殊格式的样式 */\r\n.deepseek-format-container {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 5px;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n/* DeepSeek响应内容的特定样式 */\r\n::v-deep .deepseek-response {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n::v-deep .deepseek-response pre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  display: block;\r\n  margin: 10px 0;\r\n}\r\n\r\n::v-deep .deepseek-response code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 4px;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n::v-deep .deepseek-response table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 15px 0;\r\n}\r\n\r\n::v-deep .deepseek-response th,\r\n::v-deep .deepseek-response td {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n::v-deep .deepseek-response th {\r\n  background-color: #f2f2f2;\r\n  font-weight: bold;\r\n}\r\n\r\n::v-deep .deepseek-response h1,\r\n::v-deep .deepseek-response h2,\r\n::v-deep .deepseek-response h3,\r\n::v-deep .deepseek-response h4,\r\n::v-deep .deepseek-response h5,\r\n::v-deep .deepseek-response h6 {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n  color: #222;\r\n}\r\n\r\n::v-deep .deepseek-response a {\r\n  color: #0066cc;\r\n  text-decoration: none;\r\n}\r\n\r\n::v-deep .deepseek-response blockquote {\r\n  border-left: 4px solid #ddd;\r\n  padding-left: 15px;\r\n  margin: 15px 0;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .deepseek-response ul,\r\n::v-deep .deepseek-response ol {\r\n  padding-left: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 媒体选择区域样式 */\r\n.media-selection-section {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.media-selection-section h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.media-radio-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner {\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner i {\r\n  font-size: 14px;\r\n}\r\n\r\n.media-description {\r\n  margin-top: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.media-description small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.layout-prompt-section h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 微头条相关样式 */\r\n.tth-flow-dialog {\r\n  .tth-flow-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 600px;\r\n  }\r\n\r\n  .flow-logs-section,\r\n  .flow-images-section {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .flow-logs-section h3,\r\n  .flow-images-section h3 {\r\n    margin: 0 0 12px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-timeline {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    padding: 12px;\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  .timeline-scroll {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .progress-item {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .progress-dot {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 4px;\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #67c23a;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .progress-line {\r\n    position: absolute;\r\n    left: 3px;\r\n    top: 12px;\r\n    width: 2px;\r\n    height: 20px;\r\n    background-color: #e4e7ed;\r\n  }\r\n\r\n  .progress-content {\r\n    .progress-time {\r\n      font-size: 12px;\r\n      color: #909399;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 13px;\r\n      color: #303133;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .flow-images-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n    max-height: 500px;\r\n  }\r\n\r\n  .flow-image-item {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .flow-image-item:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .flow-image {\r\n    max-width: 100%;\r\n    max-height: 400px;\r\n    min-height: 200px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    border: 2px solid #e4e7ed;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    object-fit: contain;\r\n  }\r\n\r\n  .flow-image:hover {\r\n    transform: scale(1.02);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    border-color: #409eff;\r\n  }\r\n\r\n  .no-logs {\r\n    text-align: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.tth-article-edit-dialog {\r\n  .tth-article-edit-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .article-title-section h3,\r\n  .article-content-section h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .article-title-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .article-content-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .content-input-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .content-length-info {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    z-index: 1;\r\n  }\r\n\r\n  .text-danger {\r\n    color: #f56c6c !important;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content-over-limit .el-textarea__inner {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAynBA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAOA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,SAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,EAAA;MACAC,MAAA,EAAAJ,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAG,OAAA;MACAC,MAAA,MAAAC,QAAA;MACAC,oBAAA;MACAC,WAAA;QACAC,UAAA;QACAX,MAAA;QACAK,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,aAAA;QACAC,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;MACA;MACAC,MAAA,GACA;QACAzB,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;UAAAC,KAAA;UAAAC,KAAA;QAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAM,kBAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACAE,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,eAAA,MAAAC,iBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;MACA;MACAC,kBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,mBAAA;MAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,iBAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,OACA,KAAAlC,WAAA,CAAAmC,IAAA,GAAAC,MAAA,QACA,KAAA/C,MAAA,CAAAgD,IAAA,WAAAC,EAAA;QAAA,OAAAA,EAAA,CAAA3C,OAAA;MAAA;IAEA;IACA4C,QAAA,WAAAA,SAAA;MACA,OACA,KAAAvB,eAAA,CAAAoB,MAAA,aAAAnB,WAAA,CAAAkB,IAAA,GAAAC,MAAA;IAEA;IACAI,SAAA,WAAAA,UAAA;MACA,YAAArB,YAAA,CAAAgB,IAAA,GAAAC,MAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;;MAEA;MACA,KAAAtB,WAAA,CAAAuB,OAAA,WAAAC,IAAA;QACA,KAAAF,UAAA,CAAAE,IAAA,CAAAzE,MAAA;UACAuE,UAAA,CAAAE,IAAA,CAAAzE,MAAA;QACA;QACAuE,UAAA,CAAAE,IAAA,CAAAzE,MAAA,EAAA0E,IAAA,CAAAD,IAAA;MACA;;MAEA;MACAE,MAAA,CAAAC,MAAA,CAAAL,UAAA,EAAAC,OAAA,WAAAK,SAAA;QACA;QACAA,SAAA,CAAAC,IAAA,CACA,UAAAC,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAF,CAAA,CAAAG,UAAA,QAAAD,IAAA,CAAAD,CAAA,CAAAE,UAAA;QAAA,CACA;;QAEA;QACA,IAAAC,UAAA,GAAAN,SAAA;QACA,IAAAO,IAAA,GAAAf,KAAA,CAAAgB,cAAA,CAAAF,UAAA,CAAAD,UAAA;QAEA,KAAAZ,MAAA,CAAAc,IAAA;UACAd,MAAA,CAAAc,IAAA;QACA;;QAEA;QACAd,MAAA,CAAAc,IAAA,EAAAV,IAAA,KAAAY,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAJ,UAAA;UACAK,QAAA;UACA/D,UAAA,EAAA4C,KAAA,CAAAnE,oBAAA,CAAAiF,UAAA,CAAAnF,MAAA;UACAyF,QAAA,EAAAZ,SAAA,CAAAa,KAAA,IAAAC,GAAA,WAAAC,KAAA;YAAA,WAAAN,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAK,KAAA;cACAJ,QAAA;YAAA;UAAA,CACA;QAAA,EACA;MACA;MAEA,OAAAlB,MAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAtG,MAAA;IACAqG,OAAA,CAAAC,GAAA,MAAAjG,MAAA;IACA,KAAAkG,aAAA,MAAAvG,MAAA;IACA,KAAAwG,eAAA;IACA,KAAAC,YAAA;EACA;EACAC,KAAA;IACA;IACA/C,aAAA;MACAgD,OAAA,WAAAA,QAAAC,QAAA;QACA,KAAAC,eAAA,CAAAD,QAAA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAA7C,OAAA;MAEA,KAAA/B,WAAA;MACA;MACA,KAAAG,eAAA;MAEA,KAAAL,WAAA;MACA,KAAAG,OAAA;;MAEA,KAAA5B,WAAA,CAAAG,KAAA;MAEA,KAAAH,WAAA,CAAAE,MAAA,OAAAJ,QAAA;MACA,KAAAE,WAAA,CAAAV,MAAA,QAAAA,MAAA;MACA,KAAAU,WAAA,CAAAL,MAAA,QAAAA,MAAA;MACA,KAAAK,WAAA,CAAAC,UAAA,QAAAuB,WAAA;;MAEA;MACA,KAAAS,UAAA,QAAApB,MAAA,CAAA2F,MAAA,WAAA1C,EAAA;QAAA,OAAAA,EAAA,CAAA3C,OAAA;MAAA;;MAEA;MACA,KAAAc,UAAA,CAAAoC,OAAA,WAAAP,EAAA;QACAyC,MAAA,CAAAE,IAAA,CAAA3C,EAAA;MACA;MAEA,KAAA7B,UAAA,CAAAoC,OAAA,WAAAP,EAAA;QACA,IAAAA,EAAA,CAAA1E,IAAA,mBAAA0E,EAAA,CAAA3C,OAAA;UACAoF,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA;UACAmH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA;UACAmH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA,eAAA0E,EAAA,CAAA3C,OAAA;UACAoF,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAAvC,kBAAA,CAAAmF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,WAAA2D,EAAA,CAAAvC,kBAAA,CAAAmF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;MACA;MAEAwF,OAAA,CAAAC,GAAA,aAAA5F,WAAA;;MAEA;MACA,KAAAS,aAAA,CAAAE,MAAA;MACA,KAAAF,aAAA,CAAAG,MAAA,QAAAZ,WAAA;MACA,KAAA2G,OAAA,MAAAlG,aAAA;MACA,KAAAT,WAAA,CAAAQ,SAAA;IACA;IAEAmG,OAAA,WAAAA,QAAAtH,IAAA;MAAA,IAAAuH,MAAA;MACA,IAAAD,aAAA,EAAAtH,IAAA,EAAAwH,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAH,MAAA,CAAAI,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,QAAA;QACA;MACA;IACA;IACA;IACAC,sBAAA,WAAAA,uBAAArD,EAAA,EAAAsD,eAAA;MACA,KAAAtD,EAAA,CAAA3C,OAAA;MAEA,IAAA2C,EAAA,CAAAvC,kBAAA,KAAA6F,eAAA;QACA,KAAAX,IAAA,CAAA3C,EAAA;MACA;QACA,KAAA2C,IAAA,CAAA3C,EAAA,wBAAAsD,eAAA;MACA;MACA,KAAAC,YAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAxD,EAAA,EAAAsD,eAAA;MACA,KAAAtD,EAAA,CAAA3C,OAAA;MAEA,IAAAoG,KAAA,GAAAzD,EAAA,CAAA5C,oBAAA,CAAAsG,OAAA,CAAAJ,eAAA;MACAzB,OAAA,CAAAC,GAAA,SAAA9B,EAAA,CAAA5C,oBAAA;MACA,IAAAqG,KAAA;QACA;QACA,KAAAd,IAAA,CACA3C,EAAA,CAAA5C,oBAAA,EACA4C,EAAA,CAAA5C,oBAAA,CAAA0C,MAAA,EACAwD,eACA;MACA;QACA;QACA,IAAAK,eAAA,OAAAC,mBAAA,CAAAtC,OAAA,EAAAtB,EAAA,CAAA5C,oBAAA;QACAuG,eAAA,CAAAE,MAAA,CAAAJ,KAAA;QACA,KAAAd,IAAA,CAAA3C,EAAA,0BAAA2D,eAAA;MACA;MACA9B,OAAA,CAAAC,GAAA,SAAA9B,EAAA,CAAA5C,oBAAA;MACA,KAAAmG,YAAA;IACA;IACAO,aAAA,WAAAA,cAAAxG,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAyG,aAAA,WAAAA,cAAAzG,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA0G,cAAA,WAAAA,eAAAC,IAAA;MACA,WAAAC,cAAA,EAAAD,IAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,OAAA,CAAAG,SAAA,GAAAJ,IAAA;MACA,OAAAC,OAAA,CAAAI,WAAA,IAAAJ,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAP,IAAA;MACA,YAAAhG,eAAA,CAAAwG,QAAA,CAAAR,IAAA;IACA;IAEAS,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA,IAAAC,SAAA,QAAAZ,UAAA,CAAAW,OAAA;MACA,IAAAE,QAAA,GAAAV,QAAA,CAAAC,aAAA;MACAS,QAAA,CAAA7H,KAAA,GAAA4H,SAAA;MACAT,QAAA,CAAAW,IAAA,CAAAC,WAAA,CAAAF,QAAA;MACAA,QAAA,CAAAG,MAAA;MACAb,QAAA,CAAAc,WAAA;MACAd,QAAA,CAAAW,IAAA,CAAAI,WAAA,CAAAL,QAAA;MACA,KAAA9B,QAAA,CAAAoC,OAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,MAAA;MACA;MACA,IAAAC,QAAA,GAAAD,MAAA,CAAAV,OAAA;MACA,IAAAY,IAAA,OAAAC,IAAA,EAAAF,QAAA;QAAAG,IAAA;MAAA;MACA,IAAAC,IAAA,GAAAvB,QAAA,CAAAC,aAAA;MACAsB,IAAA,CAAAC,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;MACAG,IAAA,CAAAI,QAAA,MAAAC,MAAA,CAAAV,MAAA,CAAAW,MAAA,oBAAAD,MAAA,KAAAlF,IAAA,GACAoF,WAAA,GACA3E,KAAA;MACAoE,IAAA,CAAAQ,KAAA;MACAN,GAAA,CAAAO,eAAA,CAAAT,IAAA,CAAAC,IAAA;MACA,KAAA5C,QAAA,CAAAoC,OAAA;IACA;IAEAiB,YAAA,WAAAA,aAAAC,QAAA;MACA,IAAAA,QAAA;QACAC,MAAA,CAAAC,IAAA,CAAAF,QAAA;MACA;QACA,KAAAtD,QAAA,CAAAyD,OAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAD,QAAA,IAAAA,QAAA,CAAAhH,IAAA;QACA,KAAAqD,QAAA,CAAAyD,OAAA;QACA;MACA;MACA,KAAAzI,iBAAA,GAAA2I,QAAA;MACA,KAAA5I,eAAA;MACA;MACA,IAAA8I,YAAA,QAAAlJ,WAAA,CAAA6F,OAAA,CAAAmD,QAAA;MACA,IAAAE,YAAA;QACA,KAAAC,SAAA;UACA,IAAAC,QAAA,GAAAH,MAAA,CAAAI,GAAA,CAAAC,aAAA;UACA,IAAAF,QAAA,IAAAA,QAAA,CAAAG,OAAA;YACAH,QAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAN,YAAA;UACA;QACA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA,KAAArJ,eAAA;MACA,KAAAC,iBAAA;IACA;IACA;IACAqJ,gBAAA,WAAAA,iBAAAC,KAAA;MACA3F,OAAA,CAAAsB,KAAA,YAAAqE,KAAA,CAAAC,MAAA,CAAAC,GAAA;MACA,KAAAxE,QAAA,CAAAC,KAAA;IACA;IACA;IACAwE,eAAA,WAAAA,gBAAAH,KAAA;MACA3F,OAAA,CAAAC,GAAA,YAAA0F,KAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;IACA;IACA3F,aAAA,WAAAA,cAAAnG,EAAA;MAAA,IAAAgM,MAAA;MACA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,WAAA9B,MAAA,CAAAtK,EAAA;MACAiG,OAAA,CAAAC,GAAA,mBAAAgG,OAAA,CAAAC,GAAA,CAAAC,cAAA;MACAC,kBAAA,CAAAC,OAAA,CAAAL,KAAA,YAAAL,KAAA;QACA,QAAAA,KAAA,CAAA5B,IAAA;UACA;YACA;YACA;UACA;YACAgC,MAAA,CAAAO,sBAAA,CAAAX,KAAA,CAAAjM,IAAA;YACA;UACA;YACAqM,MAAA,CAAA1E,QAAA,CAAAyD,OAAA;YACA;UACA;YACAiB,MAAA,CAAA1E,QAAA,CAAAC,KAAA;YACA;UACA;YACAyE,MAAA,CAAA1E,QAAA,CAAAC,KAAA;YACA;QACA;MACA;IACA;IAEAgF,sBAAA,WAAAA,uBAAA5M,IAAA;MACA,IAAA6M,OAAA,GAAA7M,IAAA;MACA,IAAA8M,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;;MAEA;MACA,IAAAC,OAAA,CAAAzC,IAAA,6BAAAyC,OAAA,CAAAtM,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAA+L,OAAA,CAAAtM,MAAA;MACA,WAAAsM,OAAA,CAAAzC,IAAA,6BAAAyC,OAAA,CAAAtM,MAAA;QACA,KAAAG,WAAA,CAAAK,UAAA,GAAA8L,OAAA,CAAAtM,MAAA;MACA,WAAAsM,OAAA,CAAAzC,IAAA,2BAAAyC,OAAA,CAAAtM,MAAA;QACA,KAAAG,WAAA,CAAAM,QAAA,GAAA6L,OAAA,CAAAtM,MAAA;MACA,WAAAsM,OAAA,CAAAzC,IAAA,2BAAAyC,OAAA,CAAAtM,MAAA;QACA,KAAAG,WAAA,CAAAO,QAAA,GAAA4L,OAAA,CAAAtM,MAAA;MACA,WAAAsM,OAAA,CAAAzC,IAAA,4BAAAyC,OAAA,CAAAtM,MAAA;QACA,KAAAG,WAAA,CAAAsM,SAAA,GAAAH,OAAA,CAAAtM,MAAA;MACA;;MAEA;MACA,IAAAsM,OAAA,CAAAzC,IAAA,6BAAAyC,OAAA,CAAAlC,MAAA;QACA,IAAAsC,SAAA,QAAAtK,UAAA,CAAAuK,IAAA,CACA,UAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA,KAAA+M,OAAA,CAAAlC,MAAA;QAAA,CACA;QACA,IAAAsC,SAAA;UACA;UACA,IAAAE,WAAA,GAAAF,SAAA,CAAAlL,YAAA,CAAAmL,IAAA,WAAA5G,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAuD,OAAA,CAAAvD,OAAA;UAAA;UACA,KAAA6D,WAAA;YACA;YACAF,SAAA,CAAAlL,YAAA,CAAAqL,OAAA;cACA9D,OAAA,EAAAuD,OAAA,CAAAvD,OAAA;cACA+D,SAAA,MAAA7H,IAAA;cACA8H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAAzC,IAAA,gCAAAyC,OAAA,CAAAlC,MAAA;QACA,IAAA4C,OAAA,QAAA5K,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAyN,OAAA;UACA;UACA,IAAAJ,YAAA,GAAAI,OAAA,CAAAxL,YAAA,CAAAmL,IAAA,WAAA5G,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAuD,OAAA,CAAAvD,OAAA;UAAA;UACA,KAAA6D,YAAA;YACA;YACAI,OAAA,CAAAxL,YAAA,CAAAqL,OAAA;cACA9D,OAAA,EAAAuD,OAAA,CAAAvD,OAAA;cACA+D,SAAA,MAAA7H,IAAA;cACA8H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAAzC,IAAA,gCAAAyC,OAAA,CAAAlC,MAAA;QACA,IAAA6C,WAAA,QAAA7K,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAA0N,WAAA;UACA;UACA,IAAAL,aAAA,GAAAK,WAAA,CAAAzL,YAAA,CAAAmL,IAAA,WAAA5G,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAuD,OAAA,CAAAvD,OAAA;UAAA;UACA,KAAA6D,aAAA;YACA;YACAK,WAAA,CAAAzL,YAAA,CAAAqL,OAAA;cACA9D,OAAA,EAAAuD,OAAA,CAAAvD,OAAA;cACA+D,SAAA,MAAA7H,IAAA;cACA8H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAAzC,IAAA,6BAAAyC,OAAA,CAAAY,GAAA;QACA;QACA,IAAAZ,OAAA,CAAAY,GAAA,CAAApJ,IAAA;UACA,KAAAhC,WAAA,CAAA+K,OAAA,CAAAP,OAAA,CAAAY,GAAA;UACApH,OAAA,CAAAC,GAAA,WAAAuG,OAAA,CAAAY,GAAA;QACA;UACApH,OAAA,CAAAqH,IAAA,gBAAAb,OAAA,CAAAY,GAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAZ,OAAA,CAAAzC,IAAA;QACA,IAAAuD,MAAA,QAAAhL,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAA6N,MAAA;UACA,KAAAxG,IAAA,CAAAwG,MAAA;UACA,IAAAA,MAAA,CAAA5L,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAwG,MAAA,CAAA5L,YAAA;UACA;UACA;UACA,KAAAO,OAAA,CAAA8K,OAAA;YACAzC,MAAA;YACArB,OAAA,EAAAuD,OAAA,CAAAe,YAAA;YACA5C,QAAA,EAAA6B,OAAA,CAAA7B,QAAA;YACA6C,WAAA,EAAAhB,OAAA,CAAAgB,WAAA;YACAR,SAAA,MAAA7H,IAAA;UACA;UACA,KAAAjD,eAAA;;UAEA;UACA,KAAAuL,WAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAjB,OAAA,CAAAzC,IAAA;QACA,IAAA2D,MAAA,QAAApL,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAiO,MAAA;UACA,KAAA5G,IAAA,CAAA4G,MAAA;UACA,IAAAA,MAAA,CAAAhM,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAA4G,MAAA,CAAAhM,YAAA;UACA;;UAEA;UACA,KAAAiM,uBAAA,CAAAnB,OAAA,CAAAe,YAAA;;UAEA;UACA,KAAAE,WAAA;QACA;QACA;MACA;MACA;MACA,IAAAjB,OAAA,CAAAzC,IAAA;QACA,IAAAmD,QAAA,QAAA5K,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAyN,QAAA;UACA,KAAApG,IAAA,CAAAoG,QAAA;UACA,IAAAA,QAAA,CAAAxL,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAoG,QAAA,CAAAxL,YAAA;UACA;;UAEA;UACAwL,QAAA,CAAAxL,YAAA,CAAAqL,OAAA;YACA9D,OAAA,eAAAuD,OAAA,CAAAxF,OAAA;YACAgG,SAAA,MAAA7H,IAAA;YACA8H,WAAA;UACA;;UAEA;UACA,KAAAQ,WAAA;UACA,KAAApG,QAAA,CAAAoC,OAAA;QACA;QACA;MACA;MACA;MACA,IAAA+C,OAAA,CAAAzC,IAAA;QACA,IAAAoD,YAAA,QAAA7K,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAA0N,YAAA;UACA,KAAArG,IAAA,CAAAqG,YAAA;UACA,IAAAA,YAAA,CAAAzL,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAqG,YAAA,CAAAzL,YAAA;UACA;;UAEA;UACAyL,YAAA,CAAAzL,YAAA,CAAAqL,OAAA;YACA9D,OAAA,gBAAAuD,OAAA,CAAAxF,OAAA;YACAgG,SAAA,MAAA7H,IAAA;YACA8H,WAAA;UACA;;UAEA;UACA,KAAAQ,WAAA;UACA,KAAApG,QAAA,CAAAoC,OAAA;QACA;QACA;MACA;;MAEA;MACA,IAAA+C,OAAA,CAAAzC,IAAA;QACA;QACA,IAAA6D,OAAA,QAAAtL,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAmO,OAAA;UACA,KAAA9G,IAAA,CAAA8G,OAAA;UACA,IAAAA,OAAA,CAAAlM,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAA8G,OAAA,CAAAlM,YAAA;UACA;QACA;QACA,KAAAkC,eAAA,GAAA4I,OAAA,CAAAqB,KAAA;QACA,KAAAhK,iBAAA,GAAA2I,OAAA,CAAAvD,OAAA;QACA,KAAAtF,qBAAA;QACA,KAAA8J,WAAA;QACA;MACA;;MAEA;MACA,IAAAjB,OAAA,CAAAzC,IAAA;QACA;QACA,IAAAyC,OAAA,CAAAvD,OAAA;UACA,KAAAxF,WAAA,CAAAmB,IAAA;YACAqE,OAAA,EAAAuD,OAAA,CAAAvD,OAAA;YACA+D,SAAA,MAAA7H,IAAA;YACA4E,IAAA;UACA;QACA;QACA;QACA,IAAAyC,OAAA,CAAAgB,WAAA;UACA,KAAA9J,aAAA,CAAAkB,IAAA,CAAA4H,OAAA,CAAAgB,WAAA;QACA;QACA;QACA,UAAAhK,cAAA;UACA,KAAAA,cAAA;QACA;QACA;QACA,IAAAgJ,OAAA,CAAAvD,OAAA;UACA,KAAA5B,QAAA,CAAAoC,OAAA;UACA,KAAAjG,cAAA;QACA,WAAAgJ,OAAA,CAAAvD,OAAA,gBAAAuD,OAAA,CAAAvD,OAAA;UACA,KAAA5B,QAAA,CAAAC,KAAA;UACA,KAAA9D,cAAA;UACA,KAAAG,qBAAA;QACA;QACA;MACA;;MAEA;MACA,IAAA6I,OAAA,CAAAzC,IAAA,4BAAAyC,OAAA,CAAAY,GAAA;QACA,KAAA1J,aAAA,CAAAkB,IAAA,CAAA4H,OAAA,CAAAY,GAAA;QACA,UAAA5J,cAAA;UACA,KAAAA,cAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAoJ,QAAA;MACA,QAAAJ,OAAA,CAAAzC,IAAA;QACA;QACA;QACA;QACA;UACA/D,OAAA,CAAAC,GAAA,kBAAAuG,OAAA;UACAI,QAAA,QAAAtK,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;QACA;UACAuG,OAAA,CAAAC,GAAA,YAAAuG,OAAA;UACAI,QAAA,QAAAtK,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;UACAuG,OAAA,CAAAC,GAAA,iBAAAuG,OAAA;UACAI,QAAA,QAAAtK,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;UACAuG,OAAA,CAAAC,GAAA,cAAAvG,IAAA;UACAkN,QAAA,QAAAtK,UAAA,CAAAuK,IAAA,WAAA1I,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;MACA;MAEA,IAAAmN,QAAA;QACA;QACA,KAAA9F,IAAA,CAAA8F,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAAlL,YAAA,CAAAuC,MAAA;UACA,KAAA6C,IAAA,CAAA8F,QAAA,CAAAlL,YAAA;QACA;;QAEA;QACA,IAAAoM,WAAA,QAAA7L,OAAA,CAAA8L,SAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA1D,MAAA,KAAAsC,QAAA,CAAAnN,IAAA;QAAA,CACA;QACA,IAAAqO,WAAA;UACA,KAAA7L,OAAA,CAAA8K,OAAA;YACAzC,MAAA,EAAAsC,QAAA,CAAAnN,IAAA;YACAwJ,OAAA,EAAAuD,OAAA,CAAAe,YAAA;YACA5C,QAAA,EAAA6B,OAAA,CAAA7B,QAAA;YACA6C,WAAA,EAAAhB,OAAA,CAAAgB,WAAA;YACAR,SAAA,MAAA7H,IAAA;UACA;UACA,KAAAjD,eAAA;QACA;UACA,KAAAD,OAAA,CAAA+F,MAAA,CAAA8F,WAAA;UACA,KAAA7L,OAAA,CAAA8K,OAAA;YACAzC,MAAA,EAAAsC,QAAA,CAAAnN,IAAA;YACAwJ,OAAA,EAAAuD,OAAA,CAAAe,YAAA;YACA5C,QAAA,EAAA6B,OAAA,CAAA7B,QAAA;YACA6C,WAAA,EAAAhB,OAAA,CAAAgB,WAAA;YACAR,SAAA,MAAA7H,IAAA;UACA;UACA,KAAAjD,eAAA;QACA;QACA,KAAAuL,WAAA;MACA;IAGA;IAEAQ,cAAA,WAAAA,eAAA;MACA7B,kBAAA,CAAA8B,KAAA;IACA;IAEAC,WAAA,WAAAA,YAAAzO,IAAA;MAAA,IAAA0O,MAAA;MACA,IAAAhC,kBAAA,CAAAiC,IAAA,CAAA3O,IAAA;QACA;QACA,KAAAyL,SAAA;UACAiD,MAAA,CAAAE,cAAA;QACA;MACA;QACA,KAAAjH,QAAA,CAAAC,KAAA;MACA;IACA;IACAiH,iBAAA,WAAAA,kBAAApK,EAAA;MACA,KAAA2C,IAAA,CAAA3C,EAAA,iBAAAA,EAAA,CAAAxC,UAAA;IACA;IAEA6M,UAAA,WAAAA,WAAAxB,SAAA;MACA,IAAA1H,IAAA,OAAAH,IAAA,CAAA6H,SAAA;MACA,OAAA1H,IAAA,CAAAmJ,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAlM,kBAAA;MACA,KAAAC,eAAA;IACA;IAEAkM,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAA5K,QAAA;;MAEA;MACA,IAAA6K,gBAAA,QAAAhN,OAAA,CACA4E,MAAA,WAAA8C,MAAA;QAAA,OAAAqF,MAAA,CAAAnM,eAAA,CAAAkE,QAAA,CAAA4C,MAAA,CAAAW,MAAA;MAAA,GACAzE,GAAA,WAAA8D,MAAA;QACA;QACA,IAAAuF,YAAA,GAAAF,MAAA,CAAA1G,UAAA,CAAAqB,MAAA,CAAAV,OAAA;QACA,UAAAoB,MAAA,CAAAV,MAAA,CAAAW,MAAA,0BAAAD,MAAA,CAAA6E,YAAA;MACA,GACAC,IAAA;;MAEA;MACA,IAAAC,UAAA,MAAA/E,MAAA,MAAAvH,WAAA,QAAAuH,MAAA,CAAA4E,gBAAA;;MAEA;MACA,IAAAI,YAAA;QACAtO,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,EAAA8O,UAAA;UACA5O,KAAA;QACA;MACA;;MAEA;MACAwF,OAAA,CAAAC,GAAA,OAAAoJ,YAAA;MACA,KAAArI,OAAA,CAAAqI,YAAA;MACA,KAAAzM,kBAAA;;MAEA;MACA,IAAA0K,MAAA;QACA7N,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA+D,SAAA,MAAA7H,IAAA;UACA8H,WAAA;UACAlD,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAA2N,UAAA,QAAAhN,UAAA,CAAAyL,SAAA,CACA,UAAA5J,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAA6P,UAAA;QACA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAO,MAAA;MACA;QACA;QACA,KAAAhL,UAAA,CAAAgN,UAAA,IAAAhC,MAAA;QACA;QACA,IAAAiC,IAAA,QAAAjN,UAAA,CAAA0F,MAAA,CAAAsH,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAwC,IAAA;MACA;MAEA,KAAA7H,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACA+F,iBAAA,WAAAA,kBAAA;MACA,KAAAtM,oBAAA;MACA,KAAAiD,eAAA;IACA;IAEA;IACAsJ,wBAAA,WAAAA,yBAAA;MACA,KAAAvM,oBAAA;IACA;IAEA;IACAiD,eAAA,WAAAA,gBAAAuJ,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAnK,OAAA,mBAAAoK,aAAA,CAAApK,OAAA,IAAAqK,CAAA,UAAAC,QAAA;QAAA,IAAA5I,GAAA,EAAA6I,EAAA;QAAA,WAAAH,aAAA,CAAApK,OAAA,IAAAwK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,oBAAA,EAAAV,MAAA,CAAAhQ,MAAA,EAAA+P,KAAA;YAAA;cAAAvI,GAAA,GAAA+I,QAAA,CAAAI,CAAA;cACA,IAAAnJ,GAAA,CAAAC,IAAA;gBACAuI,MAAA,CAAAxM,WAAA,GAAAgE,GAAA,CAAAzH,IAAA;cACA;cAAAwQ,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAtK,OAAA,CAAAsB,KAAA,cAAA0I,EAAA;cACAL,MAAA,CAAAtI,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA4I,QAAA,CAAAjL,CAAA;UAAA;QAAA,GAAA8K,OAAA;MAAA;IAEA;IAEA;IACAQ,iBAAA,WAAAA,kBAAAvD,SAAA;MACA,IAAA1H,IAAA,OAAAH,IAAA,CAAA6H,SAAA;MACA,OAAA1H,IAAA,CAAAmJ,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAE,MAAA;MACA;IACA;IAEA;IACAtJ,cAAA,WAAAA,eAAAyH,SAAA;MACA,IAAA1H,IAAA,OAAAH,IAAA,CAAA6H,SAAA;MACA,IAAAwD,KAAA,OAAArL,IAAA;MACA,IAAAsL,SAAA,OAAAtL,IAAA,CAAAqL,KAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;MACA,IAAAC,UAAA,OAAAzL,IAAA,CAAAqL,KAAA;MACAI,UAAA,CAAAF,OAAA,CAAAE,UAAA,CAAAD,OAAA;MACA,IAAAE,YAAA,OAAA1L,IAAA,CAAAqL,KAAA;MACAK,YAAA,CAAAH,OAAA,CAAAG,YAAA,CAAAF,OAAA;MAEA,IAAArL,IAAA,CAAAwL,YAAA,OAAAN,KAAA,CAAAM,YAAA;QACA;MACA,WAAAxL,IAAA,CAAAwL,YAAA,OAAAL,SAAA,CAAAK,YAAA;QACA;MACA,WAAAxL,IAAA,CAAAwL,YAAA,OAAAF,UAAA,CAAAE,YAAA;QACA;MACA,WAAAxL,IAAA,CAAAwL,YAAA,OAAAD,YAAA,CAAAC,YAAA;QACA;MACA;QACA,OAAAxL,IAAA,CAAAyL,kBAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAxM,IAAA;MACA;QACA,IAAAyM,WAAA,GAAA3E,IAAA,CAAAC,KAAA,CAAA/H,IAAA,CAAAjF,IAAA;QACA;QACA,KAAAwB,MAAA,GAAAkQ,WAAA,CAAAlQ,MAAA,SAAAA,MAAA;QACA;QACA,KAAAW,WAAA,GAAAuP,WAAA,CAAAvP,WAAA;QACA;QACA,KAAAS,UAAA,GAAA8O,WAAA,CAAA9O,UAAA;QACA;QACA,KAAAN,WAAA,GAAAoP,WAAA,CAAApP,WAAA;QACA;QACA,KAAAC,OAAA,GAAAmP,WAAA,CAAAnP,OAAA;QACA;QACA,KAAA/B,MAAA,GAAAyE,IAAA,CAAAzE,MAAA,SAAAA,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAAkE,IAAA,CAAAlE,UAAA;QACA,KAAAJ,WAAA,CAAAK,UAAA,GAAAiE,IAAA,CAAAjE,UAAA;QACA,KAAAL,WAAA,CAAAM,QAAA,GAAAgE,IAAA,CAAAhE,QAAA;QACA,KAAAN,WAAA,CAAAsM,SAAA,GAAAhI,IAAA,CAAAgI,SAAA;QACA,KAAAtM,WAAA,CAAAsM,SAAA,GAAAhI,IAAA,CAAA/D,QAAA;QACA,KAAAP,WAAA,CAAAQ,SAAA;;QAEA;QACA,KAAAsB,eAAA;QACA,KAAAL,WAAA;QAEA,KAAAuF,QAAA,CAAAoC,OAAA;QACA,KAAAvG,oBAAA;MACA,SAAAoE,KAAA;QACAtB,OAAA,CAAAsB,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAmG,WAAA,WAAAA,YAAA;MAAA,IAAA4D,MAAA;MAAA,WAAAzB,kBAAA,CAAAnK,OAAA,mBAAAoK,aAAA,CAAApK,OAAA,IAAAqK,CAAA,UAAAwB,SAAA;QAAA,IAAAF,WAAA,EAAAG,GAAA;QAAA,WAAA1B,aAAA,CAAApK,OAAA,IAAAwK,CAAA,WAAAuB,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA,GAAAqB,SAAA,CAAApB,CAAA;YAAA;cACA;cACA;cACA;cAEAgB,WAAA;gBACAlQ,MAAA,EAAAmQ,MAAA,CAAAnQ,MAAA;gBACAW,WAAA,EAAAwP,MAAA,CAAAxP,WAAA;gBACAS,UAAA,EAAA+O,MAAA,CAAA/O,UAAA;gBACAN,WAAA,EAAAqP,MAAA,CAAArP,WAAA;gBACAC,OAAA,EAAAoP,MAAA,CAAApP,OAAA;gBACA/B,MAAA,EAAAmR,MAAA,CAAAnR,MAAA;gBACAO,UAAA,EAAA4Q,MAAA,CAAAhR,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAA2Q,MAAA,CAAAhR,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA0Q,MAAA,CAAAhR,WAAA,CAAAM,QAAA;gBACAC,QAAA,EAAAyQ,MAAA,CAAAhR,WAAA,CAAAO,QAAA;gBACA+L,SAAA,EAAA0E,MAAA,CAAAhR,WAAA,CAAAsM;cACA;cAAA6E,SAAA,CAAArB,CAAA;cAAAqB,SAAA,CAAApB,CAAA;cAAA,OAGA,IAAAqB,sBAAA;gBACA9R,MAAA,EAAA0R,MAAA,CAAA1R,MAAA;gBACAW,UAAA,EAAA+Q,MAAA,CAAAxP,WAAA;gBACAnC,IAAA,EAAA+M,IAAA,CAAAiF,SAAA,CAAAN,WAAA;gBACAlR,MAAA,EAAAmR,MAAA,CAAAnR,MAAA;gBACAO,UAAA,EAAA4Q,MAAA,CAAAhR,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAA2Q,MAAA,CAAAhR,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA0Q,MAAA,CAAAhR,WAAA,CAAAM,QAAA;gBACAC,QAAA,EAAAyQ,MAAA,CAAAhR,WAAA,CAAAO,QAAA;gBACA+L,SAAA,EAAA0E,MAAA,CAAAhR,WAAA,CAAAsM;cACA;YAAA;cAAA6E,SAAA,CAAApB,CAAA;cAAA;YAAA;cAAAoB,SAAA,CAAArB,CAAA;cAAAoB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAEAtK,OAAA,CAAAsB,KAAA,cAAAiK,GAAA;cACAF,MAAA,CAAAhK,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAAkK,SAAA,CAAAvM,CAAA;UAAA;QAAA,GAAAqM,QAAA;MAAA;IAEA;IAEA;IACAK,sBAAA,WAAAA,uBAAAhN,IAAA;MACA,KAAAmC,IAAA,CACA,KAAA1G,oBAAA,EACAuE,IAAA,CAAAzE,MAAA,EACA,MAAAE,oBAAA,CAAAuE,IAAA,CAAAzE,MAAA,CACA;IACA;IAEA;IACA0R,aAAA,WAAAA,cAAA;MACA;MACA,KAAA1R,MAAA,OAAAC,QAAA;MACA,KAAAU,SAAA;MACA,KAAAgB,WAAA;MACA,KAAAC,WAAA;MACA,KAAAE,WAAA;MACA,KAAAC,OAAA;MACA,KAAAK,UAAA;MACA,KAAAjC,WAAA;QACAC,UAAA;QACAX,MAAA,OAAAA,MAAA;QACAK,MAAA,OAAAA,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACA+L,SAAA;QACA9L,SAAA;MACA;MACA;MACA,KAAAK,MAAA,IACA;QACAzB,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;UAAAC,KAAA;UAAAC,KAAA;QAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAM,kBAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACA;MACA,KAAAQ,eAAA;MAEA,KAAAkF,QAAA,CAAAoC,OAAA;IACA;IAEA;IACArD,YAAA,WAAAA,aAAA;MAAA,IAAAyL,MAAA;MAAA,WAAAjC,kBAAA,CAAAnK,OAAA,mBAAAoK,aAAA,CAAApK,OAAA,IAAAqK,CAAA,UAAAgC,SAAA;QAAA,IAAA3K,GAAA,EAAA4K,QAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAApK,OAAA,IAAAwK,CAAA,WAAAgC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,CAAA,GAAA8B,SAAA,CAAA7B,CAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA,OAEA,IAAAC,oBAAA,EAAAwB,MAAA,CAAAlS,MAAA;YAAA;cAAAwH,GAAA,GAAA8K,SAAA,CAAA3B,CAAA;cACA,IAAAnJ,GAAA,CAAAC,IAAA,YAAAD,GAAA,CAAAzH,IAAA,IAAAyH,GAAA,CAAAzH,IAAA,CAAAuE,MAAA;gBACA;gBACA8N,QAAA,GAAA5K,GAAA,CAAAzH,IAAA;gBACAmS,MAAA,CAAAV,eAAA,CAAAY,QAAA;cACA;cAAAE,SAAA,CAAA7B,CAAA;cAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA6B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAtK,OAAA,CAAAsB,KAAA,cAAA0K,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAhN,CAAA;UAAA;QAAA,GAAA6M,QAAA;MAAA;IAEA;IAEA;IACAI,WAAA,WAAAA,YAAA9E,GAAA;MACA,KAAAA,GAAA;MACA,IAAA+E,eAAA,IACA,QACA,SACA,QACA,QACA,QACA,SACA,OACA;MACA,IAAAC,QAAA,GAAAhF,GAAA,CAAAiF,WAAA;MACA,OAAAF,eAAA,CAAAjO,IAAA,WAAAoO,GAAA;QAAA,OAAAF,QAAA,CAAArL,QAAA,CAAAuL,GAAA;MAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAnF,GAAA;MACA,KAAAA,GAAA;MACA,OAAAA,GAAA,CAAAiF,WAAA,GAAAtL,QAAA;IACA;IAEA;IACAyL,aAAA,WAAAA,cAAAlI,MAAA;MACA,IAAAmI,QAAA;QACAC,QAAA;QACAC,EAAA;QACAC,IAAA;MACA;MAEA,IAAAC,KAAA,GAAAJ,QAAA,CAAAnI,MAAA;;MAEA;QACAuI,KAAA,EAAAA,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAApJ,MAAA;MACA,KAAA1G,mBAAA,GAAA0G,MAAA;MACA,KAAAqJ,gBAAA,CAAArJ,MAAA;IACA;IAEA;IACAqJ,gBAAA,WAAAA,iBAAArJ,MAAA;MACA,KAAA1G,mBAAA,GAAA0G,MAAA;MACA,KAAA5G,mBAAA;MACA;MACA,KAAAyD,eAAA,MAAAlD,aAAA;IACA;IAEA;IACAkD,eAAA,WAAAA,gBAAAyM,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtD,kBAAA,CAAAnK,OAAA,mBAAAoK,aAAA,CAAApK,OAAA,IAAAqK,CAAA,UAAAqD,SAAA;QAAA,IAAAC,UAAA,EAAAC,QAAA,EAAAC,GAAA;QAAA,WAAAzD,aAAA,CAAApK,OAAA,IAAAwK,CAAA,WAAAsD,SAAA;UAAA,kBAAAA,SAAA,CAAApD,CAAA,GAAAoD,SAAA,CAAAnD,CAAA;YAAA;cAAA,IACA6C,KAAA;gBAAAM,SAAA,CAAAnD,CAAA;gBAAA;cAAA;cAAA,OAAAmD,SAAA,CAAAtO,CAAA;YAAA;cAGA,IAAAgO,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA;cAAAG,SAAA,CAAApD,CAAA;cAAAoD,SAAA,CAAAnD,CAAA;cAAA,OAGA,IAAAoD,sBAAA,EAAAJ,UAAA;YAAA;cAAAC,QAAA,GAAAE,SAAA,CAAAjD,CAAA;cACA,IAAA+C,QAAA,CAAAjM,IAAA;gBACA8L,MAAA,CAAAlQ,YAAA,GAAAqQ,QAAA,CAAA3T,IAAA,aAAAwT,MAAA,CAAAjQ,mBAAA,GAAAiQ,MAAA,CAAAjQ,mBAAA,CAAAgG,OAAA;cACA;gBACA;gBACAiK,MAAA,CAAAlQ,YAAA,GAAAkQ,MAAA,CAAAO,gBAAA,CAAAR,KAAA,cAAAC,MAAA,CAAAjQ,mBAAA,GAAAiQ,MAAA,CAAAjQ,mBAAA,CAAAgG,OAAA;cACA;cAAAsK,SAAA,CAAAnD,CAAA;cAAA;YAAA;cAAAmD,SAAA,CAAApD,CAAA;cAAAmD,GAAA,GAAAC,SAAA,CAAAjD,CAAA;cAEAtK,OAAA,CAAAsB,KAAA,aAAAgM,GAAA;cACA;cACAJ,MAAA,CAAAlQ,YAAA,GAAAkQ,MAAA,CAAAO,gBAAA,CAAAR,KAAA,cAAAC,MAAA,CAAAjQ,mBAAA,GAAAiQ,MAAA,CAAAjQ,mBAAA,CAAAgG,OAAA;YAAA;cAAA,OAAAsK,SAAA,CAAAtO,CAAA;UAAA;QAAA,GAAAkO,QAAA;MAAA;IAEA;IAEA;IACAM,gBAAA,WAAAA,iBAAAR,KAAA;MACA,IAAAA,KAAA;QACA;MAUA,WAAAA,KAAA;QACA;MAaA,WAAAA,KAAA;QACA;MAUA,WAAAA,KAAA;QACA;MAaA;MACA;IACA;IAEA;IACAS,YAAA,WAAAA,aAAA;MACA,UAAArP,SAAA,UAAApB,mBAAA;MACA,KAAAF,mBAAA;MAEA,SAAAO,aAAA;QACA;QACA,KAAAqQ,uBAAA;MACA,gBAAArQ,aAAA;QACA;QACA,KAAAsQ,uBAAA;MACA,gBAAAtQ,aAAA;QACA;QACA,KAAAuQ,yBAAA;MACA;QACA;QACA,KAAAC,sBAAA;MACA;IACA;IACA;IACAH,uBAAA,WAAAA,wBAAA;MACA,IAAAzG,OAAA;QACAzN,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA+D,SAAA,MAAA7H,IAAA;UACA8H,WAAA;UACAlD,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAA2N,UAAA,QAAAhN,UAAA,CAAAyL,SAAA,CACA,UAAA5J,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAA6P,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAG,OAAA;MACA;QACA,KAAA5K,UAAA,CAAAgN,UAAA,IAAApC,OAAA;QACA,IAAA6G,KAAA,QAAAzR,UAAA,CAAA0F,MAAA,CAAAsH,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAgH,KAAA;MACA;;MAEA;MACA,IAAAC,YAAA;QACAjT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;UACA2Q,WAAA,OAAAhR,mBAAA,CAAAgG,OAAA;UACA0B,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;UACAL,MAAA,OAAArH,mBAAA,CAAAqH;QACA;MACA;MAEAtE,OAAA,CAAAC,GAAA,WAAA+N,YAAA;MACA,KAAAhN,OAAA,CAAAgN,YAAA;MACA,KAAAtM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACAoK,yBAAA,WAAAA,0BAAA;MACA,IAAA1G,WAAA;QACA1N,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA+D,SAAA,MAAA7H,IAAA;UACA8H,WAAA;UACAlD,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAA2N,UAAA,QAAAhN,UAAA,CAAAyL,SAAA,CACA,UAAA5J,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAA6P,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAI,WAAA;MACA;QACA,KAAA7K,UAAA,CAAAgN,UAAA,IAAAnC,WAAA;QACA,IAAA+G,SAAA,QAAA5R,UAAA,CAAA0F,MAAA,CAAAsH,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAI,WAAA;MACA;;MAEA;MACA,IAAAgH,gBAAA;QACApT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;UACA2Q,WAAA,OAAAhR,mBAAA,CAAAgG,OAAA;UACA0B,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;UACAL,MAAA,OAAArH,mBAAA,CAAAqH;QACA;MACA;MAEAtE,OAAA,CAAAC,GAAA,YAAAkO,gBAAA;MACA,KAAAnN,OAAA,CAAAmN,gBAAA;MACA,KAAAzM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACAqK,sBAAA,WAAAA,uBAAA;MACA,IAAAM,aAAA;QACArT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;QACA;MACA;MAEA0C,OAAA,CAAAC,GAAA,YAAAmO,aAAA;MACA,KAAApN,OAAA,CAAAoN,aAAA;MAEA,IAAA1G,MAAA;QACAjO,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA+D,SAAA,MAAA7H,IAAA;UACA8H,WAAA;UACAlD,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAA2N,UAAA,QAAAhN,UAAA,CAAAyL,SAAA,CACA,UAAA5J,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAA6P,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAW,MAAA;MACA;QACA,KAAApL,UAAA,CAAAgN,UAAA,IAAA5B,MAAA;QACA,IAAA2G,IAAA,QAAA/R,UAAA,CAAA0F,MAAA,CAAAsH,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAsH,IAAA;MACA;MAEA,KAAA3M,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IAEA;IACAmK,uBAAA,WAAAA,wBAAA;MACA;MACA,IAAAU,WAAA,QAAArS,OAAA,CAAA4K,IAAA,WAAAmB,CAAA;QAAA,OAAAA,CAAA,CAAA1D,MAAA;MAAA;MACA,IAAAiK,YAAA,GAAAD,WAAA,GAAAA,WAAA,CAAArL,OAAA;MAEA,IAAAmL,aAAA;QACArT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,KAAA+J,MAAA,CAAAkK,YAAA,QAAAlK,MAAA,MAAArH,YAAA;UACAxC,KAAA;QACA;MACA;MAEAwF,OAAA,CAAAC,GAAA,YAAAmO,aAAA;MACA,KAAApN,OAAA,CAAAoN,aAAA;MAEA,IAAAxG,OAAA;QACAnO,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA+D,SAAA,MAAA7H,IAAA;UACA8H,WAAA;UACAlD,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAA2N,UAAA,QAAAhN,UAAA,CAAAyL,SAAA,CACA,UAAA5J,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAA6P,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAa,OAAA;MACA;QACA,KAAAtL,UAAA,CAAAgN,UAAA,IAAA1B,OAAA;QACA,IAAA4G,KAAA,QAAAlS,UAAA,CAAA0F,MAAA,CAAAsH,UAAA;QACA,KAAAhN,UAAA,CAAAyK,OAAA,CAAAyH,KAAA;MACA;MAEA,KAAA9M,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IAEA;IACAkE,uBAAA,WAAAA,wBAAAsG,WAAA;MAAA,IAAAQ,OAAA;MACA,SAAApR,eAAA;MACA,KAAAgE,QAAA,CAAAoC,OAAA;MACA,KAAApG,eAAA;MACA,KAAAD,aAAA;MAEA,IAAAnC,MAAA;QACAgT,WAAA,EAAAA,WAAA;QACAtJ,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;QACAhL,MAAA,OAAAA,MAAA;QACA+U,GAAA,OAAAtR,aAAA;QACAkH,MAAA,OAAArH,mBAAA,CAAAqH;MACA;MAEA,IAAAqK,oBAAA,EAAA1T,MAAA,EACAiG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAqN,OAAA,CAAApN,QAAA,CAAAoC,OAAA;QACA;UACAgL,OAAA,CAAApN,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAyN,GAAA;QACA;MACA,GACAC,KAAA,WAAAvN,KAAA;QACAtB,OAAA,CAAAsB,KAAA,cAAAA,KAAA;QACAmN,OAAA,CAAApN,QAAA,CAAAC,KAAA;MACA,GACAwN,OAAA;QACAL,OAAA,CAAApR,eAAA;MACA;IACA;IAIA;IACA0R,iBAAA,WAAAA,kBAAA;MACA,UAAAnR,eAAA,UAAAC,iBAAA;QACA,KAAAwD,QAAA,CAAAyD,OAAA;QACA;MACA;MACA;MACA,IAAAkK,cAAA;QACAjU,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAQ,KAAA;UACAqN,KAAA,OAAAjK,eAAA;UACAqF,OAAA,OAAApF,iBAAA;UACAkG,IAAA;QACA;MACA;MACA;MACA/D,OAAA,CAAAC,GAAA,YAAA+O,cAAA;MACA,KAAAhO,OAAA,CAAAgO,cAAA;MACA,KAAArR,qBAAA;MACA;MACA,KAAAH,cAAA;MACA,KAAAC,WAAA;MACA,KAAAC,aAAA;MACA,KAAA2D,QAAA,CAAAoC,OAAA;IACA;IAGA;IACAwL,kBAAA,WAAAA,mBAAA;MACA,KAAAzR,cAAA;IACA;EACA;AACA", "ignoreList": []}]}