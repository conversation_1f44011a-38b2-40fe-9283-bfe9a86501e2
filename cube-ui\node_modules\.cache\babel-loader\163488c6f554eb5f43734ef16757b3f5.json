{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098438104}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\babel.config.js", "mtime": 1754055039760}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754057742816}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_marked", "require", "_aigc", "_uuid", "_websocket", "_interopRequireDefault", "_store", "_turndown", "name", "data", "userId", "store", "state", "user", "id", "corpId", "corp_id", "chatId", "uuidv4", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "tyChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "method", "params", "aiList", "avatar", "capabilities", "label", "value", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "selectedCapability", "promptInput", "taskStarted", "autoPlay", "screenshots", "results", "activeResultTab", "activeCollapses", "showImageDialog", "currentLargeImage", "enabledAIs", "turndownService", "TurndownService", "headingStyle", "codeBlockStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreDialogVisible", "selectedResults", "scorePrompt", "layoutDialogVisible", "layoutPrompt", "currentLayoutResult", "historyDrawerVisible", "chatHistory", "pushOfficeNum", "pushingToWechat", "selectedMedia", "pushingToMedia", "tthFlowVisible", "tthFlowLogs", "tthFlowImages", "tthArticleEditVisible", "tthArticleTitle", "tthArticleContent", "computed", "canSend", "trim", "length", "some", "ai", "canScore", "canLayout", "groupedHistory", "_this", "groups", "chatGroups", "for<PERSON>ach", "item", "push", "Object", "values", "chatGroup", "sort", "a", "b", "Date", "createTime", "parentItem", "date", "getHistoryDate", "_objectSpread2", "default", "isParent", "children", "slice", "map", "child", "created", "console", "log", "initWebSocket", "loadChatHistory", "loadLastChat", "watch", "handler", "newMedia", "loadMediaPrompt", "immediate", "methods", "sendPrompt", "_this2", "filter", "$set", "includes", "message", "_this3", "then", "res", "code", "$message", "error", "messages", "selectSingleCapability", "capabilityValue", "$forceUpdate", "toggleCapability", "index", "indexOf", "newCapabilities", "_toConsumableArray2", "splice", "getStatusText", "getStatusIcon", "renderMarkdown", "text", "marked", "htmlToText", "html", "tempDiv", "document", "createElement", "innerHTML", "textContent", "innerText", "htmlToMarkdown", "turndown", "copyResult", "content", "plainText", "textarea", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "success", "exportResult", "result", "markdown", "blob", "Blob", "type", "link", "href", "URL", "createObjectURL", "download", "concat", "aiName", "toISOString", "click", "revokeObjectURL", "openShareUrl", "shareUrl", "window", "open", "warning", "showLargeImage", "imageUrl", "_this4", "currentIndex", "$nextTick", "carousel", "$el", "querySelector", "__vue__", "setActiveItem", "closeLargeImage", "_this5", "wsUrl", "process", "env", "VUE_APP_WS_API", "websocketClient", "connect", "event", "handleWebSocketMessage", "datastr", "dataObj", "JSON", "parse", "maxChatId", "targetAI", "find", "existingLog", "unshift", "timestamp", "isCompleted", "zhihuAI", "baijiahaoAI", "url", "wkpfAI", "draftContent", "shareImgUrl", "saveHistory", "znpbAI", "pushToWechatWithContent", "tthpbAI", "title", "resultIndex", "findIndex", "r", "closeWebSocket", "close", "sendMessage", "_this6", "send", "scrollToBottom", "toggleAIExpansion", "formatTime", "toLocaleTimeString", "hour", "minute", "second", "hour12", "showScoreDialog", "handleScore", "_this7", "selectedContents", "plainContent", "join", "fullPrompt", "scoreRequest", "existIndex", "wkpf", "showHistoryDrawer", "handleHistoryDrawerClose", "isAll", "_this8", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "p", "n", "getChatHistory", "v", "formatHistoryTime", "today", "yesterday", "setDate", "getDate", "twoDaysAgo", "threeDaysAgo", "toDateString", "toLocaleDateString", "year", "month", "day", "loadHistoryItem", "historyData", "_this9", "_callee2", "_t2", "_context2", "saveUserChatData", "stringify", "toggleHistoryExpansion", "createNewChat", "_this0", "_callee3", "lastChat", "_t3", "_context3", "isImageFile", "imageExtensions", "url<PERSON><PERSON><PERSON>", "toLowerCase", "ext", "isPdfFile", "getImageStyle", "widthMap", "DeepSeek", "豆包", "通义千问", "width", "height", "handlePushToMedia", "showLayoutDialog", "media", "_this1", "_callee4", "platformId", "response", "_t4", "_context4", "getMediaCallWord", "getDefaultPrompt", "handleLayout", "createZhihuDeliveryTask", "createToutiaoLayoutTask", "createBaijiahaoLayoutTask", "createWechatLayoutTask", "zhihu", "zhihuRequest", "contentText", "b<PERSON><PERSON><PERSON><PERSON>", "baijiahaoRequest", "layoutRequest", "znpb", "scoreResult", "scoreContent", "tthpb", "_this10", "num", "pushAutoOffice", "msg", "catch", "finally", "confirmTTHPublish", "publishRequest", "closeTTHFlowDialog"], "sources": ["src/views/wechat/chrome/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\" />\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img\r\n              :src=\"require('../../../assets/ai/celan.png')\"\r\n              alt=\"历史记录\"\r\n              class=\"history-icon\"\r\n            />\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer\r\n      title=\"历史会话记录\"\r\n      :visible.sync=\"historyDrawerVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\"\r\n    >\r\n      <div class=\"history-content\">\r\n        <div\r\n          v-for=\"(group, date) in groupedHistory\"\r\n          :key=\"date\"\r\n          class=\"history-group\"\r\n        >\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div\r\n              v-for=\"(item, index) in group\"\r\n              :key=\"index\"\r\n              class=\"history-item\"\r\n            >\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i\r\n                    :class=\"[\r\n                      'el-icon-arrow-right',\r\n                      { 'is-expanded': item.isExpanded },\r\n                    ]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"\r\n                  ></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">\r\n                  {{ formatHistoryTime(item.createTime) }}\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-if=\"\r\n                  item.children && item.children.length > 0 && item.isExpanded\r\n                \"\r\n                class=\"history-children\"\r\n              >\r\n                <div\r\n                  v-for=\"(child, childIndex) in item.children\"\r\n                  :key=\"childIndex\"\r\n                  class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\"\r\n                >\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">\r\n                    {{ formatHistoryTime(child.createTime) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card\r\n                v-for=\"(ai, index) in aiList\"\r\n                :key=\"index\"\r\n                class=\"ai-card\"\r\n                shadow=\"hover\"\r\n              >\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\" />\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch\r\n                      v-model=\"ai.enabled\"\r\n                      active-color=\"#13ce66\"\r\n                      inactive-color=\"#ff4949\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <!-- 通义只支持单选-->\r\n                  <div v-if=\"ai.name === '通义千问'\" class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapability === capability.value ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"ai.selectedCapability !== capability.value\"\r\n                      @click=\"selectSingleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                  <!-- 其他AI -->\r\n                  <div v-else class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\"\r\n                      size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入提示词，支持Markdown格式\"\r\n              v-model=\"promptInput\"\r\n              resize=\"none\"\r\n              class=\"prompt-input\"\r\n            >\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"sendPrompt\"\r\n                :disabled=\"!canSend\"\r\n                class=\"send-button\"\r\n              >\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div\r\n                  v-for=\"(ai, index) in enabledAIs\"\r\n                  :key=\"index\"\r\n                  class=\"task-item\"\r\n                >\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i\r\n                        :class=\"[\r\n                          'el-icon-arrow-right',\r\n                          { 'is-expanded': ai.isExpanded },\r\n                        ]\"\r\n                      ></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{\r\n                        getStatusText(ai.status)\r\n                      }}</span>\r\n                      <i\r\n                        :class=\"getStatusIcon(ai.status)\"\r\n                        class=\"status-icon\"\r\n                      ></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div\r\n                    class=\"progress-timeline\"\r\n                    v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\"\r\n                  >\r\n                    <div class=\"timeline-scroll\">\r\n                      <div\r\n                        v-for=\"(log, logIndex) in ai.progressLogs\"\r\n                        :key=\"logIndex\"\r\n                        class=\"progress-item\"\r\n                        :class=\"{\r\n                          completed: log.isCompleted || logIndex > 0,\r\n                          current: !log.isCompleted && logIndex === 0,\r\n                        }\"\r\n                      >\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div\r\n                          class=\"progress-line\"\r\n                          v-if=\"logIndex < ai.progressLogs.length - 1\"\r\n                        ></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">\r\n                            {{ formatTime(log.timestamp) }}\r\n                          </div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch\r\n                    v-model=\"autoPlay\"\r\n                    active-text=\"自动轮播\"\r\n                    inactive-text=\"手动切换\"\r\n                  >\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel\r\n                  :interval=\"3000\"\r\n                  :autoplay=\"false\"\r\n                  indicator-position=\"outside\"\r\n                  height=\"700px\"\r\n                >\r\n                  <el-carousel-item\r\n                    v-for=\"(screenshot, index) in screenshots\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <img\r\n                      :src=\"screenshot\"\r\n                      alt=\"执行截图\"\r\n                      class=\"screenshot-image\"\r\n                      @click=\"showLargeImage(screenshot)\"\r\n                    />\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane\r\n            v-for=\"(result, index) in results\"\r\n            :key=\"index\"\r\n            :label=\"result.aiName\"\r\n            :name=\"'result-' + index\"\r\n          >\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-link\"\r\n                    @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\"\r\n                  >\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"success\"\r\n                    icon=\"el-icon-s-promotion\"\r\n                    @click=\"handlePushToMedia(result)\"\r\n                    class=\"push-media-btn\"\r\n                    :loading=\"pushingToMedia\"\r\n                    :disabled=\"pushingToMedia\"\r\n                  >\r\n                    投递到媒体\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img\r\n                  v-if=\"isImageFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  alt=\"分享图片\"\r\n                  class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\"\r\n                />\r\n                <!-- 渲染PDF -->\r\n                <iframe\r\n                  v-else-if=\"isPdfFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  class=\"share-pdf\"\r\n                  frameborder=\"0\"\r\n                >\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"openShareUrl(result.shareImgUrl)\"\r\n                  >\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"markdown-content\"\r\n                v-html=\"renderMarkdown(result.content)\"\r\n              ></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  @click=\"copyResult(result.content)\"\r\n                  >复制（纯文本）</el-button\r\n                >\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"success\"\r\n                  @click=\"exportResult(result)\"\r\n                  >导出（MD文件）</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"showImageDialog\"\r\n      width=\"90%\"\r\n      :show-close=\"true\"\r\n      :modal=\"true\"\r\n      center\r\n      class=\"image-dialog\"\r\n      :append-to-body=\"true\"\r\n      @close=\"closeLargeImage\"\r\n    >\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div\r\n          v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\"\r\n          class=\"single-image-container\"\r\n        >\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\" />\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel\r\n          v-else\r\n          :interval=\"3000\"\r\n          :autoplay=\"false\"\r\n          indicator-position=\"outside\"\r\n          height=\"80vh\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(screenshot, index) in screenshots\"\r\n            :key=\"index\"\r\n          >\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\" />\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog\r\n      title=\"智能评分\"\r\n      :visible.sync=\"scoreDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\"\r\n    >\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"10\"\r\n            placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\"\r\n            v-model=\"scorePrompt\"\r\n            resize=\"none\"\r\n            class=\"score-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox\r\n              v-for=\"(result, index) in results\"\r\n              :key=\"index\"\r\n              :label=\"result.aiName\"\r\n              class=\"result-checkbox\"\r\n            >\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 投递到媒体弹窗 -->\r\n    <el-dialog\r\n      title=\"媒体投递设置\"\r\n      :visible.sync=\"layoutDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"layout-dialog\"\r\n    >\r\n      <div class=\"layout-dialog-content\">\r\n        <!-- 媒体选择区域 -->\r\n        <div class=\"media-selection-section\">\r\n          <h3>选择投递媒体：</h3>\r\n          <el-radio-group v-model=\"selectedMedia\" size=\"small\" class=\"media-radio-group\">\r\n            <el-radio-button label=\"wechat\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              公众号\r\n            </el-radio-button>\r\n            <el-radio-button label=\"zhihu\">\r\n              <i class=\"el-icon-document\"></i>\r\n              知乎\r\n            </el-radio-button>\r\n            <el-radio-button label=\"toutiao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              微头条\r\n            </el-radio-button>\r\n            <el-radio-button label=\"baijiahao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              百家号\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n          <div class=\"media-description\">\r\n            <template v-if=\"selectedMedia === 'wechat'\">\r\n              <small>📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'zhihu'\">\r\n              <small>📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>📰 将内容转换为微头条文章格式，支持文章编辑和发布</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>🔈 将内容转换为百家号帖子格式，直接投递到百家号草稿箱</small>\r\n            </template>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"layout-prompt-section\">\r\n          <h3>排版提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"12\"\r\n            placeholder=\"请输入排版提示词\"\r\n            v-model=\"layoutPrompt\"\r\n            resize=\"none\"\r\n            class=\"layout-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"layoutDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleLayout\" :disabled=\"!canLayout\">\r\n          排版后智能投递\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条发布流程弹窗 -->\r\n    <el-dialog title=\"微头条发布流程\" :visible.sync=\"tthFlowVisible\" width=\"60%\" height=\"60%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-flow-dialog\">\r\n      <div class=\"tth-flow-content\">\r\n        <div class=\"flow-logs-section\">\r\n          <h3>发布流程日志：</h3>\r\n          <div class=\"progress-timeline\">\r\n            <div class=\"timeline-scroll\">\r\n              <div v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" class=\"progress-item completed\">\r\n                <div class=\"progress-dot\"></div>\r\n                <div v-if=\"index < tthFlowLogs.length - 1\" class=\"progress-line\"></div>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                  <div class=\"progress-text\">{{ log.content }}</div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"tthFlowLogs.length === 0\" class=\"no-logs\">暂无流程日志...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flow-images-section\">\r\n          <h3>发布流程图片：</h3>\r\n          <div class=\"flow-images-container\">\r\n            <template v-if=\"tthFlowImages.length > 0\">\r\n              <div v-for=\"(image, index) in tthFlowImages\" :key=\"index\" class=\"flow-image-item\">\r\n                <img :src=\"image\" alt=\"流程图片\" class=\"flow-image\" @click=\"showLargeImage(image)\">\r\n              </div>\r\n            </template>\r\n            <div v-else class=\"no-logs\">暂无流程图片...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeTTHFlowDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条文章编辑弹窗 -->\r\n    <el-dialog title=\"微头条文章编辑\" :visible.sync=\"tthArticleEditVisible\" width=\"70%\" height=\"80%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-article-edit-dialog\">\r\n      <div class=\"tth-article-edit-content\">\r\n        <div class=\"article-title-section\">\r\n          <h3>文章标题：</h3>\r\n          <el-input v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" class=\"article-title-input\"></el-input>\r\n        </div>\r\n        <div class=\"article-content-section\">\r\n          <h3>文章内容：</h3>\r\n          <div class=\"content-input-wrapper\">\r\n            <el-input \r\n              type=\"textarea\" \r\n              v-model=\"tthArticleContent\" \r\n              :rows=\"20\" \r\n              placeholder=\"请输入文章内容\"\r\n              resize=\"none\" \r\n              class=\"article-content-input\"\r\n              :class=\"{ 'content-over-limit': tthArticleContent.length > 2000 }\"\r\n            ></el-input>\r\n            <div class=\"content-length-info\" :class=\"{ 'text-danger': tthArticleContent.length > 2000 }\">\r\n              字数：{{ tthArticleContent.length }}/2000\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"tthArticleEditVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmTTHPublish\" :disabled=\"!tthArticleTitle || !tthArticleContent\">\r\n          确定发布\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { marked } from \"marked\";\r\nimport {\r\n  message,\r\n  saveUserChatData,\r\n  getChatHistory,\r\n  pushAutoOffice,\r\n  getMediaCallWord,\r\n} from \"@/api/wechat/aigc\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport websocketClient from \"@/utils/websocket\";\r\nimport store from \"@/store\";\r\nimport TurndownService from \"turndown\";\r\n\r\nexport default {\r\n  name: \"AIManagementPlatform\",\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: \"\",\r\n        userId: \"\",\r\n        corpId: \"\",\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        isNewChat: true,\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"\",\r\n        params: {},\r\n      },\r\n      aiList: [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: \"\",\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: \"result-0\",\r\n      activeCollapses: [\"ai-selection\", \"prompt-input\"], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: \"\",\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: \"atx\",\r\n        codeBlockStyle: \"fenced\",\r\n        emDelimiter: \"*\",\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇内容，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      layoutDialogVisible: false,\r\n      layoutPrompt: \"\",\r\n      currentLayoutResult: null, // 当前要排版的结果\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n      selectedMedia: \"wechat\", // 默认选择公众号\r\n      pushingToMedia: false, // 投递到媒体的loading状态\r\n      // 微头条相关变量\r\n      tthFlowVisible: false, // 微头条发布流程弹窗\r\n      tthFlowLogs: [], // 微头条发布流程日志\r\n      tthFlowImages: [], // 微头条发布流程图片\r\n      tthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n      tthArticleTitle: '', // 微头条文章标题\r\n      tthArticleContent: '', // 微头条文章内容\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return (\r\n        this.promptInput.trim().length > 0 &&\r\n        this.aiList.some((ai) => ai.enabled)\r\n      );\r\n    },\r\n    canScore() {\r\n      return (\r\n        this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0\r\n      );\r\n    },\r\n    canLayout() {\r\n      return this.layoutPrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach((item) => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach((chatGroup) => {\r\n        // 按时间排序\r\n        chatGroup.sort(\r\n          (a, b) => new Date(a.createTime) - new Date(b.createTime)\r\n        );\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map((child) => ({\r\n            ...child,\r\n            isParent: false,\r\n          })),\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    },\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  watch: {\r\n    // 监听媒体选择变化，自动加载对应的提示词\r\n    selectedMedia: {\r\n      handler(newMedia) {\r\n        this.loadMediaPrompt(newMedia);\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots = [];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = \"\";\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter((ai) => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach((ai) => {\r\n        this.$set(ai, \"status\", \"running\");\r\n      });\r\n\r\n      this.enabledAIs.forEach((ai) => {\r\n        if (ai.name === \"DeepSeek\" && ai.enabled) {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"deepseek,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-lwss,\";\r\n          }\r\n        }\r\n        if (ai.name === \"豆包\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db-sdsk,\";\r\n          }\r\n        }\r\n        if (ai.name === \"MiniMax Chat\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n          }\r\n        }\r\n        if(ai.name === '通义千问' && ai.enabled){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n          if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n          } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq);\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\";\r\n      this.jsonRpcReqest.params = this.userInfoReq;\r\n      this.message(this.jsonRpcReqest);\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then((res) => {\r\n        if (res.code == 201) {\r\n          this.$message.error(res.messages || '操作失败');\r\n        }\r\n      });\r\n    },\r\n    // 处理通义单选逻辑\r\n    selectSingleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      if (ai.selectedCapability === capabilityValue) {\r\n        this.$set(ai, 'selectedCapability', '');\r\n      } else {\r\n        this.$set(ai, 'selectedCapability', capabilityValue);\r\n      }\r\n      this.$forceUpdate();\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log(\"切换前:\", ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(\r\n          ai.selectedCapabilities,\r\n          ai.selectedCapabilities.length,\r\n          capabilityValue\r\n        );\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, \"selectedCapabilities\", newCapabilities);\r\n      }\r\n      console.log(\"切换后:\", ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"等待中\";\r\n        case \"running\":\r\n          return \"正在执行\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"failed\":\r\n          return \"执行失败\";\r\n        default:\r\n          return \"未知状态\";\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"el-icon-time\";\r\n        case \"running\":\r\n          return \"el-icon-loading\";\r\n        case \"completed\":\r\n          return \"el-icon-check success-icon\";\r\n        case \"failed\":\r\n          return \"el-icon-close error-icon\";\r\n        default:\r\n          return \"el-icon-question\";\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement(\"div\");\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || \"\";\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement(\"textarea\");\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textarea);\r\n      this.$message.success(\"已复制纯文本到剪贴板\");\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: \"text/markdown\" });\r\n      const link = document.createElement(\"a\");\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date()\r\n        .toISOString()\r\n        .slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success(\"已导出Markdown文件\");\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, \"_blank\");\r\n      } else {\r\n        this.$message.warning(\"暂无原链接\");\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector(\".image-dialog .el-carousel\");\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = \"\";\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log(\"WebSocket URL:\", process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case \"open\":\r\n            // this.$message.success('');\r\n            break;\r\n          case \"message\":\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case \"close\":\r\n            this.$message.warning(\"WebSocket连接已关闭\");\r\n            break;\r\n          case \"error\":\r\n            this.$message.error(\"WebSocket连接错误\");\r\n            break;\r\n          case \"reconnect_failed\":\r\n            this.$message.error(\"WebSocket重连失败，请刷新页面重试\");\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === \"RETURN_YBT1_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_YBDS_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_DB_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.tyChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.maxChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_LOG\" && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(\r\n          (ai) => ai.name === dataObj.aiName\r\n        );\r\n        if (targetAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = targetAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            targetAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到知乎\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            zhihuAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到百家号\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            baijiahaoAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理截图消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_IMG\" && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n      // 处理智能评分结果\r\n      if (dataObj.type === \"RETURN_WKPF_RES\") {\r\n        const wkpfAI = this.enabledAIs.find((ai) => ai.name === \"智能评分\");\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, \"status\", \"completed\");\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: \"智能评分\",\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能排版结果\r\n      if (dataObj.type === \"RETURN_ZNPB_RES\") {\r\n        const znpbAI = this.enabledAIs.find((ai) => ai.name === \"智能排版\");\r\n        if (znpbAI) {\r\n          this.$set(znpbAI, \"status\", \"completed\");\r\n          if (znpbAI.progressLogs.length > 0) {\r\n            this.$set(znpbAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 直接调用投递到公众号的方法，不添加到结果展示\r\n          this.pushToWechatWithContent(dataObj.draftContent);\r\n\r\n          // 智能排版完成时，保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_ZHIHU_DELIVERY_RES\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          this.$set(zhihuAI, \"status\", \"completed\");\r\n          if (zhihuAI.progressLogs.length > 0) {\r\n            this.$set(zhihuAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          zhihuAI.progressLogs.unshift({\r\n            content: \"知乎投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 知乎投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"知乎投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_BAIJIAHAO_DELIVERY_RES\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          this.$set(baijiahaoAI, \"status\", \"completed\");\r\n          if (baijiahaoAI.progressLogs.length > 0) {\r\n            this.$set(baijiahaoAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          baijiahaoAI.progressLogs.unshift({\r\n            content: \"百家号投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 百家号投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"百家号投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理微头条排版结果\r\n      if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n        // 微头条排版AI节点状态设为已完成\r\n        const tthpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n        if (tthpbAI) {\r\n          this.$set(tthpbAI, 'status', 'completed');\r\n          if (tthpbAI.progressLogs.length > 0) {\r\n            this.$set(tthpbAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n        }\r\n        this.tthArticleTitle = dataObj.title || '';\r\n        this.tthArticleContent = dataObj.content || '';\r\n        this.tthArticleEditVisible = true;\r\n        this.saveHistory();\r\n        return;\r\n      }\r\n\r\n      // 处理微头条发布流程\r\n      if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n        // 添加流程日志\r\n        if (dataObj.content) {\r\n          this.tthFlowLogs.push({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            type: 'flow',\r\n          });\r\n        }\r\n        // 处理图片信息\r\n        if (dataObj.shareImgUrl) {\r\n          this.tthFlowImages.push(dataObj.shareImgUrl);\r\n        }\r\n        // 确保流程弹窗显示\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        // 检查发布结果\r\n        if (dataObj.content === 'success') {\r\n          this.$message.success('发布到微头条成功！');\r\n          this.tthFlowVisible = true;\r\n        } else if (dataObj.content === 'false' || dataObj.content === false) {\r\n          this.$message.error('发布到微头条失败！');\r\n          this.tthFlowVisible = false;\r\n          this.tthArticleEditVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 兼容后端发送的RETURN_PC_TTH_IMG类型图片消息\r\n      if (dataObj.type === 'RETURN_PC_TTH_IMG' && dataObj.url) {\r\n        this.tthFlowImages.push(dataObj.url);\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case \"RETURN_YBT1_RES\":\r\n        case \"RETURN_TURBOS_RES\":\r\n        case \"RETURN_TURBOS_LARGE_RES\":\r\n        case \"RETURN_DEEPSEEK_RES\":\r\n          console.log(\"收到DeepSeek消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"DeepSeek\");\r\n          break;\r\n        case \"RETURN_YBDS_RES\":\r\n        case \"RETURN_DB_RES\":\r\n          console.log(\"收到豆包消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"豆包\");\r\n          break;\r\n        case \"RETURN_MAX_RES\":\r\n          console.log(\"收到MiniMax消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n          break;\r\n        case 'RETURN_TY_RES':\r\n          console.log('收到通义千问消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n          break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, \"status\", \"completed\");\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], \"isCompleted\", true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(\r\n          (r) => r.aiName === targetAI.name\r\n        );\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error(\"WebSocket未连接\");\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, \"isExpanded\", !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter((result) => this.selectedResults.includes(result.aiName))\r\n        .map((result) => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join(\"\\n\");\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"AI评分\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: \"zj-db-sdsk\", // 默认使用豆包进行评分\r\n        },\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest);\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: \"智能评分\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"智能评分任务已提交，正在评分...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"智能评分\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"智能评分\"\r\n      );\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"评分请求已发送，请等待结果\");\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return \"今天\";\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return \"昨天\";\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return \"两天前\";\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return \"三天前\";\r\n      } else {\r\n        return date.toLocaleDateString(\"zh-CN\", {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || \"\";\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || \"\";\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || \"\";\r\n        this.userInfoReq.dbChatId = item.dbChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.tyChatId || \"\";\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success(\"历史记录加载成功\");\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId,\r\n        tyChatId: this.userInfoReq.tyChatId,\r\n        maxChatId: this.userInfoReq.maxChatId,\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n          maxChatId: this.userInfoReq.maxChatId,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"保存历史记录失败:\", error);\r\n        this.$message.error(\"保存历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(\r\n        this.expandedHistoryItems,\r\n        item.chatId,\r\n        !this.expandedHistoryItems[item.chatId]\r\n      );\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = \"\";\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: \"\",\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        maxChatId: \"\",\r\n        isNewChat: true,\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n\r\n      this.$message.success(\"已创建新对话\");\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId, 0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载上次会话失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = [\r\n        \".jpg\",\r\n        \".jpeg\",\r\n        \".png\",\r\n        \".gif\",\r\n        \".bmp\",\r\n        \".webp\",\r\n        \".svg\",\r\n      ];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some((ext) => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes(\".pdf\");\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        DeepSeek: \"700px\",\r\n        豆包: \"560px\",\r\n        通义千问: \"700px\",\r\n      };\r\n\r\n      const width = widthMap[aiName] || \"560px\"; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: \"auto\",\r\n      };\r\n    },\r\n\r\n    // 投递到媒体\r\n    handlePushToMedia(result) {\r\n      this.currentLayoutResult = result;\r\n      this.showLayoutDialog(result);\r\n    },\r\n\r\n    // 显示智能排版对话框\r\n    showLayoutDialog(result) {\r\n      this.currentLayoutResult = result;\r\n      this.layoutDialogVisible = true;\r\n      // 加载当前选择媒体的提示词\r\n      this.loadMediaPrompt(this.selectedMedia);\r\n    },\r\n\r\n    // 加载媒体提示词\r\n    async loadMediaPrompt(media) {\r\n      if (!media) return;\r\n\r\n      let platformId;\r\n      if(media === 'wechat'){\r\n        platformId = 'wechat_layout';\r\n      }else if(media === 'zhihu'){\r\n        platformId = 'zhihu_layout';\r\n      }else if(media === 'baijiahao'){\r\n        platformId = 'baijiahao_layout';\r\n      }else if(media === 'toutiao'){\r\n        platformId = 'weitoutiao_layout';\r\n      }\r\n\r\n      try {\r\n        const response = await getMediaCallWord(platformId);\r\n        if (response.code === 200) {\r\n          this.layoutPrompt = response.data + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        } else {\r\n          // 使用默认提示词\r\n          this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载提示词失败:', error);\r\n        // 使用默认提示词\r\n        this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n      }\r\n    },\r\n\r\n    // 获取默认提示词(仅在后端访问失败时使用)\r\n    getDefaultPrompt(media) {\r\n      if (media === 'wechat') {\r\n        return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：`;\r\n      } else if (media === 'zhihu') {\r\n        return `请将以下内容整理为适合知乎发布的Markdown格式文章。要求：\r\n1. 保持内容的专业性和可读性\r\n2. 使用合适的标题层级（## ### #### 等）\r\n3. 代码块使用\\`\\`\\`标记，并指定语言类型\r\n4. 重要信息使用**加粗**标记\r\n5. 列表使用- 或1. 格式\r\n6. 删除不必要的格式标记\r\n7. 确保内容适合知乎的阅读习惯\r\n8. 文章结构清晰，逻辑连贯\r\n9. 目标是作为一篇专业文章投递到知乎草稿箱\r\n\r\n请对以下内容进行排版：`;\r\n\r\n      }else if (media === 'baijiahao') {\r\n        return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括\"一、\"，\"1.\"等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话`;\r\n\r\n      }else if (media === 'toutiao') {\r\n        return `根据智能评分内容，写一篇微头条文章，只能包含标题和内容，要求如下：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数严格控制在1000字以上，2000字以下\r\n7. 强制要求：只能回答标题和内容，标题必须用英文双引号（\"\"）引用起来，且放在首位，不能有其他多余的话\r\n8. 严格要求：AI必须严格遵守所有严格条件，不要输出其他多余的内容，只要标题和内容\r\n9. 内容不允许出现编号，要正常文章格式\r\n\r\n请对以下内容进行排版：`;\r\n      }\r\n      return '请对以下内容进行排版：';\r\n    },\r\n\r\n    // 处理智能排版\r\n    handleLayout() {\r\n      if (!this.canLayout || !this.currentLayoutResult) return;\r\n      this.layoutDialogVisible = false;\r\n\r\n      if (this.selectedMedia === 'zhihu') {\r\n        // 知乎投递：直接创建投递任务\r\n        this.createZhihuDeliveryTask();\r\n      } else if (this.selectedMedia === 'toutiao') {\r\n        // 微头条投递：创建微头条排版任务\r\n        this.createToutiaoLayoutTask();\r\n      } else if (this.selectedMedia === 'baijiahao') {\r\n        // 百家号投递：创建百家号排版任务\r\n        this.createBaijiahaoLayoutTask();\r\n      }else {\r\n        // 公众号投递：创建排版任务\r\n        this.createWechatLayoutTask();\r\n      }\r\n    },\r\n// 创建知乎投递任务（独立任务）\r\n    createZhihuDeliveryTask() {\r\n      const zhihuAI = {\r\n        name: \"投递到知乎\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"知乎投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到知乎\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在知乎投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到知乎\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(zhihuAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = zhihuAI;\r\n        const zhihu = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(zhihu);\r\n      }\r\n\r\n      // 发送知乎投递请求\r\n      const zhihuRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到知乎\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"zhihu\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"知乎投递参数\", zhihuRequest);\r\n      this.message(zhihuRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"知乎投递任务已创建，正在处理...\");\r\n    },\r\n    // 创建百家号投递任务（独立任务）\r\n    createBaijiahaoLayoutTask() {\r\n      const baijiahaoAI = {\r\n        name: \"投递到百家号\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"百家号投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到百家号\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在百家号投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到百家号\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = baijiahaoAI;\r\n        const baijiahao = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      }\r\n\r\n      // 发送百家号投递请求\r\n      const baijiahaoRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到百家号\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"baijiahao\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"百家号投递参数\", baijiahaoRequest);\r\n      this.message(baijiahaoRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"百家号投递任务已创建，正在处理...\");\r\n    },\r\n      // 创建公众号排版任务（保持原有逻辑）\r\n      createWechatLayoutTask() {\r\n        const layoutRequest = {\r\n          jsonrpc: \"2.0\",\r\n          id: uuidv4(),\r\n          method: \"AI排版\",\r\n          params: {\r\n            taskId: uuidv4(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: this.layoutPrompt,\r\n            roles: \"\",\r\n            selectedMedia: \"wechat\",\r\n          },\r\n        };\r\n\r\n        console.log(\"公众号排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        const znpbAI = {\r\n          name: \"智能排版\",\r\n          avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"running\",\r\n          progressLogs: [\r\n            {\r\n              content: \"智能排版任务已提交，正在排版...\",\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: \"智能排版\",\r\n            },\r\n          ],\r\n          isExpanded: true,\r\n        };\r\n\r\n        // 检查是否已存在智能排版任务\r\n        const existIndex = this.enabledAIs.findIndex(\r\n          (ai) => ai.name === \"智能排版\"\r\n        );\r\n        if (existIndex === -1) {\r\n          this.enabledAIs.unshift(znpbAI);\r\n        } else {\r\n          this.enabledAIs[existIndex] = znpbAI;\r\n          const znpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n          this.enabledAIs.unshift(znpb);\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.$message.success(\"排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 创建微头条排版任务\r\n    createToutiaoLayoutTask() {\r\n      // 获取智能评分内容\r\n      const scoreResult = this.results.find(r => r.aiName === '智能评分');\r\n      const scoreContent = scoreResult ? scoreResult.content : '';\r\n\r\n      const layoutRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"微头条排版\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: `${scoreContent}\\n${this.layoutPrompt}`,\r\n          roles: \"\",\r\n        },\r\n      };\r\n\r\n      console.log(\"微头条排版参数\", layoutRequest);\r\n      this.message(layoutRequest);\r\n\r\n      const tthpbAI = {\r\n        name: \"微头条排版\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"微头条排版任务已提交，正在排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"微头条排版\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在微头条排版任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"微头条排版\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(tthpbAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = tthpbAI;\r\n        const tthpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(tthpb);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"微头条排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 实际投递到公众号\r\n    pushToWechatWithContent(contentText) {\r\n      if (this.pushingToWechat) return;\r\n      this.$message.success(\"开始投递公众号！\");\r\n      this.pushingToWechat = true;\r\n      this.pushOfficeNum += 1;\r\n\r\n      const params = {\r\n        contentText: contentText,\r\n        shareUrl: this.currentLayoutResult.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: this.currentLayoutResult.aiName,\r\n      };\r\n\r\n      pushAutoOffice(params)\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.$message.success(\"投递到公众号成功！\");\r\n          } else {\r\n            this.$message.error(res.msg || \"投递失败，请重试\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"投递到公众号失败:\", error);\r\n          this.$message.error(\"投递失败，请重试\");\r\n        })\r\n        .finally(() => {\r\n          this.pushingToWechat = false;\r\n        });\r\n    },\r\n\r\n\r\n\r\n    // 确认微头条发布\r\n    confirmTTHPublish() {\r\n      if (!this.tthArticleTitle || !this.tthArticleContent) {\r\n        this.$message.warning('请填写标题和内容');\r\n        return;\r\n      }\r\n      // 构建微头条发布请求\r\n      const publishRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n                  method: '微头条发布',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          roles: '',\r\n          title: this.tthArticleTitle,\r\n          content: this.tthArticleContent,\r\n          type: '微头条发布'\r\n        }\r\n      };\r\n      // 发送发布请求\r\n      console.log(\"微头条发布参数\", publishRequest);\r\n      this.message(publishRequest);\r\n      this.tthArticleEditVisible = false;\r\n      // 显示流程弹窗\r\n      this.tthFlowVisible = true;\r\n      this.tthFlowLogs = [];\r\n      this.tthFlowImages = [];\r\n      this.$message.success('微头条发布请求已发送！');\r\n    },\r\n\r\n\r\n    // 关闭微头条发布流程弹窗\r\n    closeTTHFlowDialog() {\r\n      this.tthFlowVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card,\r\n.screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn,\r\n.push-media-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.layout-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.layout-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.layout-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.layout-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.layout-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.layout-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409eff;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 用于处理DeepSeek特殊格式的样式 */\r\n.deepseek-format-container {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 5px;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n/* DeepSeek响应内容的特定样式 */\r\n::v-deep .deepseek-response {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n::v-deep .deepseek-response pre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  display: block;\r\n  margin: 10px 0;\r\n}\r\n\r\n::v-deep .deepseek-response code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 4px;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n::v-deep .deepseek-response table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 15px 0;\r\n}\r\n\r\n::v-deep .deepseek-response th,\r\n::v-deep .deepseek-response td {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n::v-deep .deepseek-response th {\r\n  background-color: #f2f2f2;\r\n  font-weight: bold;\r\n}\r\n\r\n::v-deep .deepseek-response h1,\r\n::v-deep .deepseek-response h2,\r\n::v-deep .deepseek-response h3,\r\n::v-deep .deepseek-response h4,\r\n::v-deep .deepseek-response h5,\r\n::v-deep .deepseek-response h6 {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n  color: #222;\r\n}\r\n\r\n::v-deep .deepseek-response a {\r\n  color: #0066cc;\r\n  text-decoration: none;\r\n}\r\n\r\n::v-deep .deepseek-response blockquote {\r\n  border-left: 4px solid #ddd;\r\n  padding-left: 15px;\r\n  margin: 15px 0;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .deepseek-response ul,\r\n::v-deep .deepseek-response ol {\r\n  padding-left: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 媒体选择区域样式 */\r\n.media-selection-section {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.media-selection-section h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.media-radio-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner {\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner i {\r\n  font-size: 14px;\r\n}\r\n\r\n.media-description {\r\n  margin-top: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.media-description small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.layout-prompt-section h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 微头条相关样式 */\r\n.tth-flow-dialog {\r\n  .tth-flow-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 600px;\r\n  }\r\n\r\n  .flow-logs-section,\r\n  .flow-images-section {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .flow-logs-section h3,\r\n  .flow-images-section h3 {\r\n    margin: 0 0 12px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-timeline {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    padding: 12px;\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  .timeline-scroll {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .progress-item {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .progress-dot {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 4px;\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #67c23a;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .progress-line {\r\n    position: absolute;\r\n    left: 3px;\r\n    top: 12px;\r\n    width: 2px;\r\n    height: 20px;\r\n    background-color: #e4e7ed;\r\n  }\r\n\r\n  .progress-content {\r\n    .progress-time {\r\n      font-size: 12px;\r\n      color: #909399;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 13px;\r\n      color: #303133;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .flow-images-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n    max-height: 500px;\r\n  }\r\n\r\n  .flow-image-item {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .flow-image-item:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .flow-image {\r\n    max-width: 100%;\r\n    max-height: 400px;\r\n    min-height: 200px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    border: 2px solid #e4e7ed;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    object-fit: contain;\r\n  }\r\n\r\n  .flow-image:hover {\r\n    transform: scale(1.02);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    border-color: #409eff;\r\n  }\r\n\r\n  .no-logs {\r\n    text-align: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.tth-article-edit-dialog {\r\n  .tth-article-edit-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .article-title-section h3,\r\n  .article-content-section h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .article-title-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .article-content-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .content-input-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .content-length-info {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    z-index: 1;\r\n  }\r\n\r\n  .text-danger {\r\n    color: #f56c6c !important;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content-over-limit .el-textarea__inner {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAulBA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAOA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,SAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,EAAA;MACAC,MAAA,EAAAJ,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAG,OAAA;MACAC,MAAA,MAAAC,QAAA;MACAC,oBAAA;MACAC,WAAA;QACAC,UAAA;QACAX,MAAA;QACAK,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,aAAA;QACAC,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;MACA;MACAC,MAAA,GACA;QACAzB,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;UAAAC,KAAA;UAAAC,KAAA;QAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAM,kBAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACAE,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,eAAA,MAAAC,iBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;MACA;MACAC,kBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,mBAAA;MAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,eAAA;MAAA;MACAC,aAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,iBAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,OACA,KAAAlC,WAAA,CAAAmC,IAAA,GAAAC,MAAA,QACA,KAAA/C,MAAA,CAAAgD,IAAA,WAAAC,EAAA;QAAA,OAAAA,EAAA,CAAA3C,OAAA;MAAA;IAEA;IACA4C,QAAA,WAAAA,SAAA;MACA,OACA,KAAAvB,eAAA,CAAAoB,MAAA,aAAAnB,WAAA,CAAAkB,IAAA,GAAAC,MAAA;IAEA;IACAI,SAAA,WAAAA,UAAA;MACA,YAAArB,YAAA,CAAAgB,IAAA,GAAAC,MAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;;MAEA;MACA,KAAAtB,WAAA,CAAAuB,OAAA,WAAAC,IAAA;QACA,KAAAF,UAAA,CAAAE,IAAA,CAAAzE,MAAA;UACAuE,UAAA,CAAAE,IAAA,CAAAzE,MAAA;QACA;QACAuE,UAAA,CAAAE,IAAA,CAAAzE,MAAA,EAAA0E,IAAA,CAAAD,IAAA;MACA;;MAEA;MACAE,MAAA,CAAAC,MAAA,CAAAL,UAAA,EAAAC,OAAA,WAAAK,SAAA;QACA;QACAA,SAAA,CAAAC,IAAA,CACA,UAAAC,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAF,CAAA,CAAAG,UAAA,QAAAD,IAAA,CAAAD,CAAA,CAAAE,UAAA;QAAA,CACA;;QAEA;QACA,IAAAC,UAAA,GAAAN,SAAA;QACA,IAAAO,IAAA,GAAAf,KAAA,CAAAgB,cAAA,CAAAF,UAAA,CAAAD,UAAA;QAEA,KAAAZ,MAAA,CAAAc,IAAA;UACAd,MAAA,CAAAc,IAAA;QACA;;QAEA;QACAd,MAAA,CAAAc,IAAA,EAAAV,IAAA,KAAAY,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAJ,UAAA;UACAK,QAAA;UACA/D,UAAA,EAAA4C,KAAA,CAAAnE,oBAAA,CAAAiF,UAAA,CAAAnF,MAAA;UACAyF,QAAA,EAAAZ,SAAA,CAAAa,KAAA,IAAAC,GAAA,WAAAC,KAAA;YAAA,WAAAN,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAK,KAAA;cACAJ,QAAA;YAAA;UAAA,CACA;QAAA,EACA;MACA;MAEA,OAAAlB,MAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAtG,MAAA;IACAqG,OAAA,CAAAC,GAAA,MAAAjG,MAAA;IACA,KAAAkG,aAAA,MAAAvG,MAAA;IACA,KAAAwG,eAAA;IACA,KAAAC,YAAA;EACA;EACAC,KAAA;IACA;IACA/C,aAAA;MACAgD,OAAA,WAAAA,QAAAC,QAAA;QACA,KAAAC,eAAA,CAAAD,QAAA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAA7C,OAAA;MAEA,KAAA/B,WAAA;MACA;MACA,KAAAG,eAAA;MAEA,KAAAL,WAAA;MACA,KAAAG,OAAA;;MAEA,KAAA5B,WAAA,CAAAG,KAAA;MAEA,KAAAH,WAAA,CAAAE,MAAA,OAAAJ,QAAA;MACA,KAAAE,WAAA,CAAAV,MAAA,QAAAA,MAAA;MACA,KAAAU,WAAA,CAAAL,MAAA,QAAAA,MAAA;MACA,KAAAK,WAAA,CAAAC,UAAA,QAAAuB,WAAA;;MAEA;MACA,KAAAS,UAAA,QAAApB,MAAA,CAAA2F,MAAA,WAAA1C,EAAA;QAAA,OAAAA,EAAA,CAAA3C,OAAA;MAAA;;MAEA;MACA,KAAAc,UAAA,CAAAoC,OAAA,WAAAP,EAAA;QACAyC,MAAA,CAAAE,IAAA,CAAA3C,EAAA;MACA;MAEA,KAAA7B,UAAA,CAAAoC,OAAA,WAAAP,EAAA;QACA,IAAAA,EAAA,CAAA1E,IAAA,mBAAA0E,EAAA,CAAA3C,OAAA;UACAoF,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA;UACAmH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA;UACAmH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;UACA,IAAA2D,EAAA,CAAA5C,oBAAA,CAAAwF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA2D,EAAA,CAAA1E,IAAA,eAAA0E,EAAA,CAAA3C,OAAA;UACAoF,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,IAAA2D,EAAA,CAAAvC,kBAAA,CAAAmF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA,WAAA2D,EAAA,CAAAvC,kBAAA,CAAAmF,QAAA;YACAH,MAAA,CAAAvG,WAAA,CAAAG,KAAA,GAAAoG,MAAA,CAAAvG,WAAA,CAAAG,KAAA;UACA;QACA;MACA;MAEAwF,OAAA,CAAAC,GAAA,aAAA5F,WAAA;;MAEA;MACA,KAAAS,aAAA,CAAAE,MAAA;MACA,KAAAF,aAAA,CAAAG,MAAA,QAAAZ,WAAA;MACA,KAAA2G,OAAA,MAAAlG,aAAA;MACA,KAAAT,WAAA,CAAAQ,SAAA;IACA;IAEAmG,OAAA,WAAAA,QAAAtH,IAAA;MAAA,IAAAuH,MAAA;MACA,IAAAD,aAAA,EAAAtH,IAAA,EAAAwH,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAH,MAAA,CAAAI,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,QAAA;QACA;MACA;IACA;IACA;IACAC,sBAAA,WAAAA,uBAAArD,EAAA,EAAAsD,eAAA;MACA,KAAAtD,EAAA,CAAA3C,OAAA;MAEA,IAAA2C,EAAA,CAAAvC,kBAAA,KAAA6F,eAAA;QACA,KAAAX,IAAA,CAAA3C,EAAA;MACA;QACA,KAAA2C,IAAA,CAAA3C,EAAA,wBAAAsD,eAAA;MACA;MACA,KAAAC,YAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAxD,EAAA,EAAAsD,eAAA;MACA,KAAAtD,EAAA,CAAA3C,OAAA;MAEA,IAAAoG,KAAA,GAAAzD,EAAA,CAAA5C,oBAAA,CAAAsG,OAAA,CAAAJ,eAAA;MACAzB,OAAA,CAAAC,GAAA,SAAA9B,EAAA,CAAA5C,oBAAA;MACA,IAAAqG,KAAA;QACA;QACA,KAAAd,IAAA,CACA3C,EAAA,CAAA5C,oBAAA,EACA4C,EAAA,CAAA5C,oBAAA,CAAA0C,MAAA,EACAwD,eACA;MACA;QACA;QACA,IAAAK,eAAA,OAAAC,mBAAA,CAAAtC,OAAA,EAAAtB,EAAA,CAAA5C,oBAAA;QACAuG,eAAA,CAAAE,MAAA,CAAAJ,KAAA;QACA,KAAAd,IAAA,CAAA3C,EAAA,0BAAA2D,eAAA;MACA;MACA9B,OAAA,CAAAC,GAAA,SAAA9B,EAAA,CAAA5C,oBAAA;MACA,KAAAmG,YAAA;IACA;IACAO,aAAA,WAAAA,cAAAxG,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACAyG,aAAA,WAAAA,cAAAzG,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IACA0G,cAAA,WAAAA,eAAAC,IAAA;MACA,WAAAC,cAAA,EAAAD,IAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,OAAA,CAAAG,SAAA,GAAAJ,IAAA;MACA,OAAAC,OAAA,CAAAI,WAAA,IAAAJ,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAP,IAAA;MACA,YAAAhG,eAAA,CAAAwG,QAAA,CAAAR,IAAA;IACA;IAEAS,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA,IAAAC,SAAA,QAAAZ,UAAA,CAAAW,OAAA;MACA,IAAAE,QAAA,GAAAV,QAAA,CAAAC,aAAA;MACAS,QAAA,CAAA7H,KAAA,GAAA4H,SAAA;MACAT,QAAA,CAAAW,IAAA,CAAAC,WAAA,CAAAF,QAAA;MACAA,QAAA,CAAAG,MAAA;MACAb,QAAA,CAAAc,WAAA;MACAd,QAAA,CAAAW,IAAA,CAAAI,WAAA,CAAAL,QAAA;MACA,KAAA9B,QAAA,CAAAoC,OAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,MAAA;MACA;MACA,IAAAC,QAAA,GAAAD,MAAA,CAAAV,OAAA;MACA,IAAAY,IAAA,OAAAC,IAAA,EAAAF,QAAA;QAAAG,IAAA;MAAA;MACA,IAAAC,IAAA,GAAAvB,QAAA,CAAAC,aAAA;MACAsB,IAAA,CAAAC,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;MACAG,IAAA,CAAAI,QAAA,MAAAC,MAAA,CAAAV,MAAA,CAAAW,MAAA,oBAAAD,MAAA,KAAAlF,IAAA,GACAoF,WAAA,GACA3E,KAAA;MACAoE,IAAA,CAAAQ,KAAA;MACAN,GAAA,CAAAO,eAAA,CAAAT,IAAA,CAAAC,IAAA;MACA,KAAA5C,QAAA,CAAAoC,OAAA;IACA;IAEAiB,YAAA,WAAAA,aAAAC,QAAA;MACA,IAAAA,QAAA;QACAC,MAAA,CAAAC,IAAA,CAAAF,QAAA;MACA;QACA,KAAAtD,QAAA,CAAAyD,OAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA5I,iBAAA,GAAA2I,QAAA;MACA,KAAA5I,eAAA;MACA;MACA,IAAA8I,YAAA,QAAAlJ,WAAA,CAAA6F,OAAA,CAAAmD,QAAA;MACA,IAAAE,YAAA;QACA,KAAAC,SAAA;UACA,IAAAC,QAAA,GAAAH,MAAA,CAAAI,GAAA,CAAAC,aAAA;UACA,IAAAF,QAAA,IAAAA,QAAA,CAAAG,OAAA;YACAH,QAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAN,YAAA;UACA;QACA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA,KAAArJ,eAAA;MACA,KAAAC,iBAAA;IACA;IACA;IACA6D,aAAA,WAAAA,cAAAnG,EAAA;MAAA,IAAA2L,MAAA;MACA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,WAAAzB,MAAA,CAAAtK,EAAA;MACAiG,OAAA,CAAAC,GAAA,mBAAA2F,OAAA,CAAAC,GAAA,CAAAC,cAAA;MACAC,kBAAA,CAAAC,OAAA,CAAAL,KAAA,YAAAM,KAAA;QACA,QAAAA,KAAA,CAAAlC,IAAA;UACA;YACA;YACA;UACA;YACA2B,MAAA,CAAAQ,sBAAA,CAAAD,KAAA,CAAAvM,IAAA;YACA;UACA;YACAgM,MAAA,CAAArE,QAAA,CAAAyD,OAAA;YACA;UACA;YACAY,MAAA,CAAArE,QAAA,CAAAC,KAAA;YACA;UACA;YACAoE,MAAA,CAAArE,QAAA,CAAAC,KAAA;YACA;QACA;MACA;IACA;IAEA4E,sBAAA,WAAAA,uBAAAxM,IAAA;MACA,IAAAyM,OAAA,GAAAzM,IAAA;MACA,IAAA0M,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;;MAEA;MACA,IAAAC,OAAA,CAAArC,IAAA,6BAAAqC,OAAA,CAAAlM,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAA2L,OAAA,CAAAlM,MAAA;MACA,WAAAkM,OAAA,CAAArC,IAAA,6BAAAqC,OAAA,CAAAlM,MAAA;QACA,KAAAG,WAAA,CAAAK,UAAA,GAAA0L,OAAA,CAAAlM,MAAA;MACA,WAAAkM,OAAA,CAAArC,IAAA,2BAAAqC,OAAA,CAAAlM,MAAA;QACA,KAAAG,WAAA,CAAAM,QAAA,GAAAyL,OAAA,CAAAlM,MAAA;MACA,WAAAkM,OAAA,CAAArC,IAAA,2BAAAqC,OAAA,CAAAlM,MAAA;QACA,KAAAG,WAAA,CAAAO,QAAA,GAAAwL,OAAA,CAAAlM,MAAA;MACA,WAAAkM,OAAA,CAAArC,IAAA,4BAAAqC,OAAA,CAAAlM,MAAA;QACA,KAAAG,WAAA,CAAAkM,SAAA,GAAAH,OAAA,CAAAlM,MAAA;MACA;;MAEA;MACA,IAAAkM,OAAA,CAAArC,IAAA,6BAAAqC,OAAA,CAAA9B,MAAA;QACA,IAAAkC,SAAA,QAAAlK,UAAA,CAAAmK,IAAA,CACA,UAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA,KAAA2M,OAAA,CAAA9B,MAAA;QAAA,CACA;QACA,IAAAkC,SAAA;UACA;UACA,IAAAE,WAAA,GAAAF,SAAA,CAAA9K,YAAA,CAAA+K,IAAA,WAAAxG,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAmD,OAAA,CAAAnD,OAAA;UAAA;UACA,KAAAyD,WAAA;YACA;YACAF,SAAA,CAAA9K,YAAA,CAAAiL,OAAA;cACA1D,OAAA,EAAAmD,OAAA,CAAAnD,OAAA;cACA2D,SAAA,MAAAzH,IAAA;cACA0H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAArC,IAAA,gCAAAqC,OAAA,CAAA9B,MAAA;QACA,IAAAwC,OAAA,QAAAxK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAqN,OAAA;UACA;UACA,IAAAJ,YAAA,GAAAI,OAAA,CAAApL,YAAA,CAAA+K,IAAA,WAAAxG,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAmD,OAAA,CAAAnD,OAAA;UAAA;UACA,KAAAyD,YAAA;YACA;YACAI,OAAA,CAAApL,YAAA,CAAAiL,OAAA;cACA1D,OAAA,EAAAmD,OAAA,CAAAnD,OAAA;cACA2D,SAAA,MAAAzH,IAAA;cACA0H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAArC,IAAA,gCAAAqC,OAAA,CAAA9B,MAAA;QACA,IAAAyC,WAAA,QAAAzK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAsN,WAAA;UACA;UACA,IAAAL,aAAA,GAAAK,WAAA,CAAArL,YAAA,CAAA+K,IAAA,WAAAxG,GAAA;YAAA,OAAAA,GAAA,CAAAgD,OAAA,KAAAmD,OAAA,CAAAnD,OAAA;UAAA;UACA,KAAAyD,aAAA;YACA;YACAK,WAAA,CAAArL,YAAA,CAAAiL,OAAA;cACA1D,OAAA,EAAAmD,OAAA,CAAAnD,OAAA;cACA2D,SAAA,MAAAzH,IAAA;cACA0H,WAAA;YACA;UACA;QACA;QACA;MACA;MACA;MACA,IAAAT,OAAA,CAAArC,IAAA,6BAAAqC,OAAA,CAAAY,GAAA;QACA;QACA,KAAAhL,WAAA,CAAA2K,OAAA,CAAAP,OAAA,CAAAY,GAAA;QACA;MACA;;MAEA;MACA,IAAAZ,OAAA,CAAArC,IAAA;QACA,IAAAkD,MAAA,QAAA3K,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAwN,MAAA;UACA,KAAAnG,IAAA,CAAAmG,MAAA;UACA,IAAAA,MAAA,CAAAvL,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAmG,MAAA,CAAAvL,YAAA;UACA;UACA;UACA,KAAAO,OAAA,CAAA0K,OAAA;YACArC,MAAA;YACArB,OAAA,EAAAmD,OAAA,CAAAc,YAAA;YACAvC,QAAA,EAAAyB,OAAA,CAAAzB,QAAA;YACAwC,WAAA,EAAAf,OAAA,CAAAe,WAAA;YACAP,SAAA,MAAAzH,IAAA;UACA;UACA,KAAAjD,eAAA;;UAEA;UACA,KAAAkL,WAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAhB,OAAA,CAAArC,IAAA;QACA,IAAAsD,MAAA,QAAA/K,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAA4N,MAAA;UACA,KAAAvG,IAAA,CAAAuG,MAAA;UACA,IAAAA,MAAA,CAAA3L,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAuG,MAAA,CAAA3L,YAAA;UACA;;UAEA;UACA,KAAA4L,uBAAA,CAAAlB,OAAA,CAAAc,YAAA;;UAEA;UACA,KAAAE,WAAA;QACA;QACA;MACA;MACA;MACA,IAAAhB,OAAA,CAAArC,IAAA;QACA,IAAA+C,QAAA,QAAAxK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAqN,QAAA;UACA,KAAAhG,IAAA,CAAAgG,QAAA;UACA,IAAAA,QAAA,CAAApL,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAgG,QAAA,CAAApL,YAAA;UACA;;UAEA;UACAoL,QAAA,CAAApL,YAAA,CAAAiL,OAAA;YACA1D,OAAA,eAAAmD,OAAA,CAAApF,OAAA;YACA4F,SAAA,MAAAzH,IAAA;YACA0H,WAAA;UACA;;UAEA;UACA,KAAAO,WAAA;UACA,KAAA/F,QAAA,CAAAoC,OAAA;QACA;QACA;MACA;MACA;MACA,IAAA2C,OAAA,CAAArC,IAAA;QACA,IAAAgD,YAAA,QAAAzK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAAsN,YAAA;UACA,KAAAjG,IAAA,CAAAiG,YAAA;UACA,IAAAA,YAAA,CAAArL,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAiG,YAAA,CAAArL,YAAA;UACA;;UAEA;UACAqL,YAAA,CAAArL,YAAA,CAAAiL,OAAA;YACA1D,OAAA,gBAAAmD,OAAA,CAAApF,OAAA;YACA4F,SAAA,MAAAzH,IAAA;YACA0H,WAAA;UACA;;UAEA;UACA,KAAAO,WAAA;UACA,KAAA/F,QAAA,CAAAoC,OAAA;QACA;QACA;MACA;;MAEA;MACA,IAAA2C,OAAA,CAAArC,IAAA;QACA;QACA,IAAAwD,OAAA,QAAAjL,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;UAAA,OAAAA,EAAA,CAAA1E,IAAA;QAAA;QACA,IAAA8N,OAAA;UACA,KAAAzG,IAAA,CAAAyG,OAAA;UACA,IAAAA,OAAA,CAAA7L,YAAA,CAAAuC,MAAA;YACA,KAAA6C,IAAA,CAAAyG,OAAA,CAAA7L,YAAA;UACA;QACA;QACA,KAAAkC,eAAA,GAAAwI,OAAA,CAAAoB,KAAA;QACA,KAAA3J,iBAAA,GAAAuI,OAAA,CAAAnD,OAAA;QACA,KAAAtF,qBAAA;QACA,KAAAyJ,WAAA;QACA;MACA;;MAEA;MACA,IAAAhB,OAAA,CAAArC,IAAA;QACA;QACA,IAAAqC,OAAA,CAAAnD,OAAA;UACA,KAAAxF,WAAA,CAAAmB,IAAA;YACAqE,OAAA,EAAAmD,OAAA,CAAAnD,OAAA;YACA2D,SAAA,MAAAzH,IAAA;YACA4E,IAAA;UACA;QACA;QACA;QACA,IAAAqC,OAAA,CAAAe,WAAA;UACA,KAAAzJ,aAAA,CAAAkB,IAAA,CAAAwH,OAAA,CAAAe,WAAA;QACA;QACA;QACA,UAAA3J,cAAA;UACA,KAAAA,cAAA;QACA;QACA;QACA,IAAA4I,OAAA,CAAAnD,OAAA;UACA,KAAA5B,QAAA,CAAAoC,OAAA;UACA,KAAAjG,cAAA;QACA,WAAA4I,OAAA,CAAAnD,OAAA,gBAAAmD,OAAA,CAAAnD,OAAA;UACA,KAAA5B,QAAA,CAAAC,KAAA;UACA,KAAA9D,cAAA;UACA,KAAAG,qBAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAyI,OAAA,CAAArC,IAAA,4BAAAqC,OAAA,CAAAY,GAAA;QACA,KAAAtJ,aAAA,CAAAkB,IAAA,CAAAwH,OAAA,CAAAY,GAAA;QACA,UAAAxJ,cAAA;UACA,KAAAA,cAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAgJ,QAAA;MACA,QAAAJ,OAAA,CAAArC,IAAA;QACA;QACA;QACA;QACA;UACA/D,OAAA,CAAAC,GAAA,kBAAAmG,OAAA;UACAI,QAAA,QAAAlK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;QACA;UACAuG,OAAA,CAAAC,GAAA,YAAAmG,OAAA;UACAI,QAAA,QAAAlK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;UACAuG,OAAA,CAAAC,GAAA,iBAAAmG,OAAA;UACAI,QAAA,QAAAlK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;QACA;UACAuG,OAAA,CAAAC,GAAA,cAAAvG,IAAA;UACA8M,QAAA,QAAAlK,UAAA,CAAAmK,IAAA,WAAAtI,EAAA;YAAA,OAAAA,EAAA,CAAA1E,IAAA;UAAA;UACA;MACA;MAEA,IAAA+M,QAAA;QACA;QACA,KAAA1F,IAAA,CAAA0F,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAA9K,YAAA,CAAAuC,MAAA;UACA,KAAA6C,IAAA,CAAA0F,QAAA,CAAA9K,YAAA;QACA;;QAEA;QACA,IAAA+L,WAAA,QAAAxL,OAAA,CAAAyL,SAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,MAAA,KAAAkC,QAAA,CAAA/M,IAAA;QAAA,CACA;QACA,IAAAgO,WAAA;UACA,KAAAxL,OAAA,CAAA0K,OAAA;YACArC,MAAA,EAAAkC,QAAA,CAAA/M,IAAA;YACAwJ,OAAA,EAAAmD,OAAA,CAAAc,YAAA;YACAvC,QAAA,EAAAyB,OAAA,CAAAzB,QAAA;YACAwC,WAAA,EAAAf,OAAA,CAAAe,WAAA;YACAP,SAAA,MAAAzH,IAAA;UACA;UACA,KAAAjD,eAAA;QACA;UACA,KAAAD,OAAA,CAAA+F,MAAA,CAAAyF,WAAA;UACA,KAAAxL,OAAA,CAAA0K,OAAA;YACArC,MAAA,EAAAkC,QAAA,CAAA/M,IAAA;YACAwJ,OAAA,EAAAmD,OAAA,CAAAc,YAAA;YACAvC,QAAA,EAAAyB,OAAA,CAAAzB,QAAA;YACAwC,WAAA,EAAAf,OAAA,CAAAe,WAAA;YACAP,SAAA,MAAAzH,IAAA;UACA;UACA,KAAAjD,eAAA;QACA;QACA,KAAAkL,WAAA;MACA;IAGA;IAEAQ,cAAA,WAAAA,eAAA;MACA7B,kBAAA,CAAA8B,KAAA;IACA;IAEAC,WAAA,WAAAA,YAAApO,IAAA;MAAA,IAAAqO,MAAA;MACA,IAAAhC,kBAAA,CAAAiC,IAAA,CAAAtO,IAAA;QACA;QACA,KAAAyL,SAAA;UACA4C,MAAA,CAAAE,cAAA;QACA;MACA;QACA,KAAA5G,QAAA,CAAAC,KAAA;MACA;IACA;IACA4G,iBAAA,WAAAA,kBAAA/J,EAAA;MACA,KAAA2C,IAAA,CAAA3C,EAAA,iBAAAA,EAAA,CAAAxC,UAAA;IACA;IAEAwM,UAAA,WAAAA,WAAAvB,SAAA;MACA,IAAAtH,IAAA,OAAAH,IAAA,CAAAyH,SAAA;MACA,OAAAtH,IAAA,CAAA8I,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAA7L,kBAAA;MACA,KAAAC,eAAA;IACA;IAEA6L,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAAvK,QAAA;;MAEA;MACA,IAAAwK,gBAAA,QAAA3M,OAAA,CACA4E,MAAA,WAAA8C,MAAA;QAAA,OAAAgF,MAAA,CAAA9L,eAAA,CAAAkE,QAAA,CAAA4C,MAAA,CAAAW,MAAA;MAAA,GACAzE,GAAA,WAAA8D,MAAA;QACA;QACA,IAAAkF,YAAA,GAAAF,MAAA,CAAArG,UAAA,CAAAqB,MAAA,CAAAV,OAAA;QACA,UAAAoB,MAAA,CAAAV,MAAA,CAAAW,MAAA,0BAAAD,MAAA,CAAAwE,YAAA;MACA,GACAC,IAAA;;MAEA;MACA,IAAAC,UAAA,MAAA1E,MAAA,MAAAvH,WAAA,QAAAuH,MAAA,CAAAuE,gBAAA;;MAEA;MACA,IAAAI,YAAA;QACAjO,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,EAAAyO,UAAA;UACAvO,KAAA;QACA;MACA;;MAEA;MACAwF,OAAA,CAAAC,GAAA,OAAA+I,YAAA;MACA,KAAAhI,OAAA,CAAAgI,YAAA;MACA,KAAApM,kBAAA;;MAEA;MACA,IAAAqK,MAAA;QACAxN,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA2D,SAAA,MAAAzH,IAAA;UACA0H,WAAA;UACA9C,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAAsN,UAAA,QAAA3M,UAAA,CAAAoL,SAAA,CACA,UAAAvJ,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAAwP,UAAA;QACA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAM,MAAA;MACA;QACA;QACA,KAAA3K,UAAA,CAAA2M,UAAA,IAAAhC,MAAA;QACA;QACA,IAAAiC,IAAA,QAAA5M,UAAA,CAAA0F,MAAA,CAAAiH,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAuC,IAAA;MACA;MAEA,KAAAxH,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACA0F,iBAAA,WAAAA,kBAAA;MACA,KAAAjM,oBAAA;MACA,KAAAiD,eAAA;IACA;IAEA;IACAiJ,wBAAA,WAAAA,yBAAA;MACA,KAAAlM,oBAAA;IACA;IAEA;IACAiD,eAAA,WAAAA,gBAAAkJ,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAA9J,OAAA,mBAAA+J,aAAA,CAAA/J,OAAA,IAAAgK,CAAA,UAAAC,QAAA;QAAA,IAAAvI,GAAA,EAAAwI,EAAA;QAAA,WAAAH,aAAA,CAAA/J,OAAA,IAAAmK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,oBAAA,EAAAV,MAAA,CAAA3P,MAAA,EAAA0P,KAAA;YAAA;cAAAlI,GAAA,GAAA0I,QAAA,CAAAI,CAAA;cACA,IAAA9I,GAAA,CAAAC,IAAA;gBACAkI,MAAA,CAAAnM,WAAA,GAAAgE,GAAA,CAAAzH,IAAA;cACA;cAAAmQ,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAjK,OAAA,CAAAsB,KAAA,cAAAqI,EAAA;cACAL,MAAA,CAAAjI,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAAuI,QAAA,CAAA5K,CAAA;UAAA;QAAA,GAAAyK,OAAA;MAAA;IAEA;IAEA;IACAQ,iBAAA,WAAAA,kBAAAtD,SAAA;MACA,IAAAtH,IAAA,OAAAH,IAAA,CAAAyH,SAAA;MACA,OAAAtH,IAAA,CAAA8I,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAE,MAAA;MACA;IACA;IAEA;IACAjJ,cAAA,WAAAA,eAAAqH,SAAA;MACA,IAAAtH,IAAA,OAAAH,IAAA,CAAAyH,SAAA;MACA,IAAAuD,KAAA,OAAAhL,IAAA;MACA,IAAAiL,SAAA,OAAAjL,IAAA,CAAAgL,KAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;MACA,IAAAC,UAAA,OAAApL,IAAA,CAAAgL,KAAA;MACAI,UAAA,CAAAF,OAAA,CAAAE,UAAA,CAAAD,OAAA;MACA,IAAAE,YAAA,OAAArL,IAAA,CAAAgL,KAAA;MACAK,YAAA,CAAAH,OAAA,CAAAG,YAAA,CAAAF,OAAA;MAEA,IAAAhL,IAAA,CAAAmL,YAAA,OAAAN,KAAA,CAAAM,YAAA;QACA;MACA,WAAAnL,IAAA,CAAAmL,YAAA,OAAAL,SAAA,CAAAK,YAAA;QACA;MACA,WAAAnL,IAAA,CAAAmL,YAAA,OAAAF,UAAA,CAAAE,YAAA;QACA;MACA,WAAAnL,IAAA,CAAAmL,YAAA,OAAAD,YAAA,CAAAC,YAAA;QACA;MACA;QACA,OAAAnL,IAAA,CAAAoL,kBAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAnM,IAAA;MACA;QACA,IAAAoM,WAAA,GAAA1E,IAAA,CAAAC,KAAA,CAAA3H,IAAA,CAAAjF,IAAA;QACA;QACA,KAAAwB,MAAA,GAAA6P,WAAA,CAAA7P,MAAA,SAAAA,MAAA;QACA;QACA,KAAAW,WAAA,GAAAkP,WAAA,CAAAlP,WAAA;QACA;QACA,KAAAS,UAAA,GAAAyO,WAAA,CAAAzO,UAAA;QACA;QACA,KAAAN,WAAA,GAAA+O,WAAA,CAAA/O,WAAA;QACA;QACA,KAAAC,OAAA,GAAA8O,WAAA,CAAA9O,OAAA;QACA;QACA,KAAA/B,MAAA,GAAAyE,IAAA,CAAAzE,MAAA,SAAAA,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAAkE,IAAA,CAAAlE,UAAA;QACA,KAAAJ,WAAA,CAAAK,UAAA,GAAAiE,IAAA,CAAAjE,UAAA;QACA,KAAAL,WAAA,CAAAM,QAAA,GAAAgE,IAAA,CAAAhE,QAAA;QACA,KAAAN,WAAA,CAAAkM,SAAA,GAAA5H,IAAA,CAAA4H,SAAA;QACA,KAAAlM,WAAA,CAAAkM,SAAA,GAAA5H,IAAA,CAAA/D,QAAA;QACA,KAAAP,WAAA,CAAAQ,SAAA;;QAEA;QACA,KAAAsB,eAAA;QACA,KAAAL,WAAA;QAEA,KAAAuF,QAAA,CAAAoC,OAAA;QACA,KAAAvG,oBAAA;MACA,SAAAoE,KAAA;QACAtB,OAAA,CAAAsB,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA8F,WAAA,WAAAA,YAAA;MAAA,IAAA4D,MAAA;MAAA,WAAAzB,kBAAA,CAAA9J,OAAA,mBAAA+J,aAAA,CAAA/J,OAAA,IAAAgK,CAAA,UAAAwB,SAAA;QAAA,IAAAF,WAAA,EAAAG,GAAA;QAAA,WAAA1B,aAAA,CAAA/J,OAAA,IAAAmK,CAAA,WAAAuB,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA,GAAAqB,SAAA,CAAApB,CAAA;YAAA;cACA;cACA;cACA;cAEAgB,WAAA;gBACA7P,MAAA,EAAA8P,MAAA,CAAA9P,MAAA;gBACAW,WAAA,EAAAmP,MAAA,CAAAnP,WAAA;gBACAS,UAAA,EAAA0O,MAAA,CAAA1O,UAAA;gBACAN,WAAA,EAAAgP,MAAA,CAAAhP,WAAA;gBACAC,OAAA,EAAA+O,MAAA,CAAA/O,OAAA;gBACA/B,MAAA,EAAA8Q,MAAA,CAAA9Q,MAAA;gBACAO,UAAA,EAAAuQ,MAAA,CAAA3Q,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAAsQ,MAAA,CAAA3Q,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAAqQ,MAAA,CAAA3Q,WAAA,CAAAM,QAAA;gBACAC,QAAA,EAAAoQ,MAAA,CAAA3Q,WAAA,CAAAO,QAAA;gBACA2L,SAAA,EAAAyE,MAAA,CAAA3Q,WAAA,CAAAkM;cACA;cAAA4E,SAAA,CAAArB,CAAA;cAAAqB,SAAA,CAAApB,CAAA;cAAA,OAGA,IAAAqB,sBAAA;gBACAzR,MAAA,EAAAqR,MAAA,CAAArR,MAAA;gBACAW,UAAA,EAAA0Q,MAAA,CAAAnP,WAAA;gBACAnC,IAAA,EAAA2M,IAAA,CAAAgF,SAAA,CAAAN,WAAA;gBACA7Q,MAAA,EAAA8Q,MAAA,CAAA9Q,MAAA;gBACAO,UAAA,EAAAuQ,MAAA,CAAA3Q,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAAsQ,MAAA,CAAA3Q,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAAqQ,MAAA,CAAA3Q,WAAA,CAAAM,QAAA;gBACAC,QAAA,EAAAoQ,MAAA,CAAA3Q,WAAA,CAAAO,QAAA;gBACA2L,SAAA,EAAAyE,MAAA,CAAA3Q,WAAA,CAAAkM;cACA;YAAA;cAAA4E,SAAA,CAAApB,CAAA;cAAA;YAAA;cAAAoB,SAAA,CAAArB,CAAA;cAAAoB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAEAjK,OAAA,CAAAsB,KAAA,cAAA4J,GAAA;cACAF,MAAA,CAAA3J,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA6J,SAAA,CAAAlM,CAAA;UAAA;QAAA,GAAAgM,QAAA;MAAA;IAEA;IAEA;IACAK,sBAAA,WAAAA,uBAAA3M,IAAA;MACA,KAAAmC,IAAA,CACA,KAAA1G,oBAAA,EACAuE,IAAA,CAAAzE,MAAA,EACA,MAAAE,oBAAA,CAAAuE,IAAA,CAAAzE,MAAA,CACA;IACA;IAEA;IACAqR,aAAA,WAAAA,cAAA;MACA;MACA,KAAArR,MAAA,OAAAC,QAAA;MACA,KAAAU,SAAA;MACA,KAAAgB,WAAA;MACA,KAAAC,WAAA;MACA,KAAAE,WAAA;MACA,KAAAC,OAAA;MACA,KAAAK,UAAA;MACA,KAAAjC,WAAA;QACAC,UAAA;QACAX,MAAA,OAAAA,MAAA;QACAK,MAAA,OAAAA,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACA2L,SAAA;QACA1L,SAAA;MACA;MACA;MACA,KAAAK,MAAA,IACA;QACAzB,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;UAAAC,KAAA;UAAAC,KAAA;QAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACAlC,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAM,kBAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACA;MACA,KAAAQ,eAAA;MAEA,KAAAkF,QAAA,CAAAoC,OAAA;IACA;IAEA;IACArD,YAAA,WAAAA,aAAA;MAAA,IAAAoL,MAAA;MAAA,WAAAjC,kBAAA,CAAA9J,OAAA,mBAAA+J,aAAA,CAAA/J,OAAA,IAAAgK,CAAA,UAAAgC,SAAA;QAAA,IAAAtK,GAAA,EAAAuK,QAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAA/J,OAAA,IAAAmK,CAAA,WAAAgC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,CAAA,GAAA8B,SAAA,CAAA7B,CAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA,OAEA,IAAAC,oBAAA,EAAAwB,MAAA,CAAA7R,MAAA;YAAA;cAAAwH,GAAA,GAAAyK,SAAA,CAAA3B,CAAA;cACA,IAAA9I,GAAA,CAAAC,IAAA,YAAAD,GAAA,CAAAzH,IAAA,IAAAyH,GAAA,CAAAzH,IAAA,CAAAuE,MAAA;gBACA;gBACAyN,QAAA,GAAAvK,GAAA,CAAAzH,IAAA;gBACA8R,MAAA,CAAAV,eAAA,CAAAY,QAAA;cACA;cAAAE,SAAA,CAAA7B,CAAA;cAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA6B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAjK,OAAA,CAAAsB,KAAA,cAAAqK,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA3M,CAAA;UAAA;QAAA,GAAAwM,QAAA;MAAA;IAEA;IAEA;IACAI,WAAA,WAAAA,YAAA7E,GAAA;MACA,KAAAA,GAAA;MACA,IAAA8E,eAAA,IACA,QACA,SACA,QACA,QACA,QACA,SACA,OACA;MACA,IAAAC,QAAA,GAAA/E,GAAA,CAAAgF,WAAA;MACA,OAAAF,eAAA,CAAA5N,IAAA,WAAA+N,GAAA;QAAA,OAAAF,QAAA,CAAAhL,QAAA,CAAAkL,GAAA;MAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAlF,GAAA;MACA,KAAAA,GAAA;MACA,OAAAA,GAAA,CAAAgF,WAAA,GAAAjL,QAAA;IACA;IAEA;IACAoL,aAAA,WAAAA,cAAA7H,MAAA;MACA,IAAA8H,QAAA;QACAC,QAAA;QACAC,EAAA;QACAC,IAAA;MACA;MAEA,IAAAC,KAAA,GAAAJ,QAAA,CAAA9H,MAAA;;MAEA;QACAkI,KAAA,EAAAA,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA/I,MAAA;MACA,KAAA1G,mBAAA,GAAA0G,MAAA;MACA,KAAAgJ,gBAAA,CAAAhJ,MAAA;IACA;IAEA;IACAgJ,gBAAA,WAAAA,iBAAAhJ,MAAA;MACA,KAAA1G,mBAAA,GAAA0G,MAAA;MACA,KAAA5G,mBAAA;MACA;MACA,KAAAyD,eAAA,MAAAlD,aAAA;IACA;IAEA;IACAkD,eAAA,WAAAA,gBAAAoM,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtD,kBAAA,CAAA9J,OAAA,mBAAA+J,aAAA,CAAA/J,OAAA,IAAAgK,CAAA,UAAAqD,SAAA;QAAA,IAAAC,UAAA,EAAAC,QAAA,EAAAC,GAAA;QAAA,WAAAzD,aAAA,CAAA/J,OAAA,IAAAmK,CAAA,WAAAsD,SAAA;UAAA,kBAAAA,SAAA,CAAApD,CAAA,GAAAoD,SAAA,CAAAnD,CAAA;YAAA;cAAA,IACA6C,KAAA;gBAAAM,SAAA,CAAAnD,CAAA;gBAAA;cAAA;cAAA,OAAAmD,SAAA,CAAAjO,CAAA;YAAA;cAGA,IAAA2N,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA,WAAAH,KAAA;gBACAG,UAAA;cACA;cAAAG,SAAA,CAAApD,CAAA;cAAAoD,SAAA,CAAAnD,CAAA;cAAA,OAGA,IAAAoD,sBAAA,EAAAJ,UAAA;YAAA;cAAAC,QAAA,GAAAE,SAAA,CAAAjD,CAAA;cACA,IAAA+C,QAAA,CAAA5L,IAAA;gBACAyL,MAAA,CAAA7P,YAAA,GAAAgQ,QAAA,CAAAtT,IAAA,aAAAmT,MAAA,CAAA5P,mBAAA,GAAA4P,MAAA,CAAA5P,mBAAA,CAAAgG,OAAA;cACA;gBACA;gBACA4J,MAAA,CAAA7P,YAAA,GAAA6P,MAAA,CAAAO,gBAAA,CAAAR,KAAA,cAAAC,MAAA,CAAA5P,mBAAA,GAAA4P,MAAA,CAAA5P,mBAAA,CAAAgG,OAAA;cACA;cAAAiK,SAAA,CAAAnD,CAAA;cAAA;YAAA;cAAAmD,SAAA,CAAApD,CAAA;cAAAmD,GAAA,GAAAC,SAAA,CAAAjD,CAAA;cAEAjK,OAAA,CAAAsB,KAAA,aAAA2L,GAAA;cACA;cACAJ,MAAA,CAAA7P,YAAA,GAAA6P,MAAA,CAAAO,gBAAA,CAAAR,KAAA,cAAAC,MAAA,CAAA5P,mBAAA,GAAA4P,MAAA,CAAA5P,mBAAA,CAAAgG,OAAA;YAAA;cAAA,OAAAiK,SAAA,CAAAjO,CAAA;UAAA;QAAA,GAAA6N,QAAA;MAAA;IAEA;IAEA;IACAM,gBAAA,WAAAA,iBAAAR,KAAA;MACA,IAAAA,KAAA;QACA;MAUA,WAAAA,KAAA;QACA;MAaA,WAAAA,KAAA;QACA;MAUA,WAAAA,KAAA;QACA;MAaA;MACA;IACA;IAEA;IACAS,YAAA,WAAAA,aAAA;MACA,UAAAhP,SAAA,UAAApB,mBAAA;MACA,KAAAF,mBAAA;MAEA,SAAAO,aAAA;QACA;QACA,KAAAgQ,uBAAA;MACA,gBAAAhQ,aAAA;QACA;QACA,KAAAiQ,uBAAA;MACA,gBAAAjQ,aAAA;QACA;QACA,KAAAkQ,yBAAA;MACA;QACA;QACA,KAAAC,sBAAA;MACA;IACA;IACA;IACAH,uBAAA,WAAAA,wBAAA;MACA,IAAAxG,OAAA;QACArN,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA2D,SAAA,MAAAzH,IAAA;UACA0H,WAAA;UACA9C,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAAsN,UAAA,QAAA3M,UAAA,CAAAoL,SAAA,CACA,UAAAvJ,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAAwP,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAG,OAAA;MACA;QACA,KAAAxK,UAAA,CAAA2M,UAAA,IAAAnC,OAAA;QACA,IAAA4G,KAAA,QAAApR,UAAA,CAAA0F,MAAA,CAAAiH,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAA+G,KAAA;MACA;;MAEA;MACA,IAAAC,YAAA;QACA5S,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;UACAsQ,WAAA,OAAA3Q,mBAAA,CAAAgG,OAAA;UACA0B,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;UACAL,MAAA,OAAArH,mBAAA,CAAAqH;QACA;MACA;MAEAtE,OAAA,CAAAC,GAAA,WAAA0N,YAAA;MACA,KAAA3M,OAAA,CAAA2M,YAAA;MACA,KAAAjM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACA+J,yBAAA,WAAAA,0BAAA;MACA,IAAAzG,WAAA;QACAtN,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA2D,SAAA,MAAAzH,IAAA;UACA0H,WAAA;UACA9C,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAAsN,UAAA,QAAA3M,UAAA,CAAAoL,SAAA,CACA,UAAAvJ,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAAwP,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAI,WAAA;MACA;QACA,KAAAzK,UAAA,CAAA2M,UAAA,IAAAlC,WAAA;QACA,IAAA8G,SAAA,QAAAvR,UAAA,CAAA0F,MAAA,CAAAiH,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAI,WAAA;MACA;;MAEA;MACA,IAAA+G,gBAAA;QACA/S,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;UACAsQ,WAAA,OAAA3Q,mBAAA,CAAAgG,OAAA;UACA0B,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;UACAL,MAAA,OAAArH,mBAAA,CAAAqH;QACA;MACA;MAEAtE,OAAA,CAAAC,GAAA,YAAA6N,gBAAA;MACA,KAAA9M,OAAA,CAAA8M,gBAAA;MACA,KAAApM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IACA;IACAgK,sBAAA,WAAAA,uBAAA;MACA,IAAAM,aAAA;QACAhT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,OAAA0C,YAAA;UACAxC,KAAA;UACA8C,aAAA;QACA;MACA;MAEA0C,OAAA,CAAAC,GAAA,YAAA8N,aAAA;MACA,KAAA/M,OAAA,CAAA+M,aAAA;MAEA,IAAA1G,MAAA;QACA5N,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA2D,SAAA,MAAAzH,IAAA;UACA0H,WAAA;UACA9C,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAAsN,UAAA,QAAA3M,UAAA,CAAAoL,SAAA,CACA,UAAAvJ,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAAwP,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAU,MAAA;MACA;QACA,KAAA/K,UAAA,CAAA2M,UAAA,IAAA5B,MAAA;QACA,IAAA2G,IAAA,QAAA1R,UAAA,CAAA0F,MAAA,CAAAiH,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAqH,IAAA;MACA;MAEA,KAAAtM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IAEA;IACA8J,uBAAA,WAAAA,wBAAA;MACA;MACA,IAAAU,WAAA,QAAAhS,OAAA,CAAAwK,IAAA,WAAAkB,CAAA;QAAA,OAAAA,CAAA,CAAArD,MAAA;MAAA;MACA,IAAA4J,YAAA,GAAAD,WAAA,GAAAA,WAAA,CAAAhL,OAAA;MAEA,IAAA8K,aAAA;QACAhT,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,KAAA+J,MAAA,CAAA6J,YAAA,QAAA7J,MAAA,MAAArH,YAAA;UACAxC,KAAA;QACA;MACA;MAEAwF,OAAA,CAAAC,GAAA,YAAA8N,aAAA;MACA,KAAA/M,OAAA,CAAA+M,aAAA;MAEA,IAAAxG,OAAA;QACA9N,IAAA;QACA0B,MAAA,EAAAjC,OAAA;QACAkC,YAAA;QACAG,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAuH,OAAA;UACA2D,SAAA,MAAAzH,IAAA;UACA0H,WAAA;UACA9C,IAAA;QACA,EACA;QACApI,UAAA;MACA;;MAEA;MACA,IAAAsN,UAAA,QAAA3M,UAAA,CAAAoL,SAAA,CACA,UAAAvJ,EAAA;QAAA,OAAAA,EAAA,CAAA1E,IAAA;MAAA,CACA;MACA,IAAAwP,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAY,OAAA;MACA;QACA,KAAAjL,UAAA,CAAA2M,UAAA,IAAA1B,OAAA;QACA,IAAA4G,KAAA,QAAA7R,UAAA,CAAA0F,MAAA,CAAAiH,UAAA;QACA,KAAA3M,UAAA,CAAAqK,OAAA,CAAAwH,KAAA;MACA;MAEA,KAAAzM,YAAA;MACA,KAAAL,QAAA,CAAAoC,OAAA;IACA;IAEA;IACA6D,uBAAA,WAAAA,wBAAAsG,WAAA;MAAA,IAAAQ,OAAA;MACA,SAAA/Q,eAAA;MACA,KAAAgE,QAAA,CAAAoC,OAAA;MACA,KAAApG,eAAA;MACA,KAAAD,aAAA;MAEA,IAAAnC,MAAA;QACA2S,WAAA,EAAAA,WAAA;QACAjJ,QAAA,OAAA1H,mBAAA,CAAA0H,QAAA;QACAhL,MAAA,OAAAA,MAAA;QACA0U,GAAA,OAAAjR,aAAA;QACAkH,MAAA,OAAArH,mBAAA,CAAAqH;MACA;MAEA,IAAAgK,oBAAA,EAAArT,MAAA,EACAiG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgN,OAAA,CAAA/M,QAAA,CAAAoC,OAAA;QACA;UACA2K,OAAA,CAAA/M,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAoN,GAAA;QACA;MACA,GACAC,KAAA,WAAAlN,KAAA;QACAtB,OAAA,CAAAsB,KAAA,cAAAA,KAAA;QACA8M,OAAA,CAAA/M,QAAA,CAAAC,KAAA;MACA,GACAmN,OAAA;QACAL,OAAA,CAAA/Q,eAAA;MACA;IACA;IAIA;IACAqR,iBAAA,WAAAA,kBAAA;MACA,UAAA9Q,eAAA,UAAAC,iBAAA;QACA,KAAAwD,QAAA,CAAAyD,OAAA;QACA;MACA;MACA;MACA,IAAA6J,cAAA;QACA5T,OAAA;QACAhB,EAAA,MAAAI,QAAA;QACAa,MAAA;QACAC,MAAA;UACAV,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAQ,KAAA;UACAgN,KAAA,OAAA5J,eAAA;UACAqF,OAAA,OAAApF,iBAAA;UACAkG,IAAA;QACA;MACA;MACA;MACA/D,OAAA,CAAAC,GAAA,YAAA0O,cAAA;MACA,KAAA3N,OAAA,CAAA2N,cAAA;MACA,KAAAhR,qBAAA;MACA;MACA,KAAAH,cAAA;MACA,KAAAC,WAAA;MACA,KAAAC,aAAA;MACA,KAAA2D,QAAA,CAAAoC,OAAA;IACA;IAGA;IACAmL,kBAAA,WAAAA,mBAAA;MACA,KAAApR,cAAA;IACA;EACA;AACA", "ignoreList": []}]}