{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=style&index=0&id=3cc1bfc8&scoped=true&lang=css", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098438104}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754057740846}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754057743497}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754057742464}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWktbWFuYWdlbWVudC1wbGF0Zm9ybSB7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBwYWRkaW5nLWJvdHRvbTogMzBweDsNCn0NCg0KLnRvcC1uYXYgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmxvZ28tYXJlYSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5sb2dvIHsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5wbGF0Zm9ybS10aXRsZSB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLm1haW4tY29udGVudCB7DQogIHBhZGRpbmc6IDAgMzBweDsNCiAgd2lkdGg6IDkwJTsNCiAgbWFyZ2luOiAwIGF1dG87DQp9DQo6OnYtZGVlcCAuZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzMzMzsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KfQ0KLnNlY3Rpb24tdGl0bGUgew0KICBmb250LXNpemU6IDE4cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouYWktY2FyZHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KICBtYXJnaW4tbGVmdDogMjBweDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg0KLmFpLWNhcmQgew0KICB3aWR0aDogY2FsYygyNSUgLSAyMHB4KTsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLmFpLWNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouYWktbGVmdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5haS1hdmF0YXIgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5haS1hdmF0YXIgaW1nIHsNCiAgd2lkdGg6IDMwcHg7DQogIGhlaWdodDogMzBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBvYmplY3QtZml0OiBjb3ZlcjsNCn0NCg0KLmFpLW5hbWUgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouYWktc3RhdHVzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmFpLWNhcGFiaWxpdGllcyB7DQogIG1hcmdpbjogMTVweCAwOw0KICB3aWR0aDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLmJ1dHRvbi1jYXBhYmlsaXR5LWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5idXR0b24tY2FwYWJpbGl0eS1ncm91cCAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luOiAwOw0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBwYWRkaW5nOiA2cHggMTJweDsNCn0NCg0KLmJ1dHRvbi1jYXBhYmlsaXR5LWdyb3VwIC5lbC1idXR0b24uaXMtcGxhaW46aG92ZXIsDQouYnV0dG9uLWNhcGFiaWxpdHktZ3JvdXAgLmVsLWJ1dHRvbi5pcy1wbGFpbjpmb2N1cyB7DQogIGJhY2tncm91bmQ6ICNlY2Y1ZmY7DQogIGJvcmRlci1jb2xvcjogI2IzZDhmZjsNCiAgY29sb3I6ICM0MDllZmY7DQp9DQoNCi5wcm9tcHQtaW5wdXQtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQogIHBhZGRpbmc6IDAgMjBweCAwIDBweDsNCn0NCg0KLnByb21wdC1pbnB1dCB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICB3aWR0aDogOTklOw0KfQ0KDQoucHJvbXB0LWZvb3RlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi1ib3R0b206IC0zMHB4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi53b3JkLWNvdW50IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQp9DQoNCi5zZW5kLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDEwcHggMjBweDsNCn0NCg0KLmV4ZWN1dGlvbi1zdGF0dXMtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQogIHBhZGRpbmc6IDIwcHggMHB4IDBweCAwcHg7DQp9DQoNCi50YXNrLWZsb3ctY2FyZCwNCi5zY3JlZW5zaG90cy1jYXJkIHsNCiAgaGVpZ2h0OiA4MDBweDsNCn0NCg0KLmNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoudGFzay1mbG93IHsNCiAgcGFkZGluZzogMTVweDsNCiAgaGVpZ2h0OiA4MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQoudGFzay1mbG93Ojotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA2cHg7DQp9DQoNCi50YXNrLWZsb3c6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2MwYzRjYzsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KfQ0KDQoudGFzay1mbG93Ojotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi50YXNrLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi50YXNrLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTJweCAxNXB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zczsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi50YXNrLWhlYWRlcjpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi5oZWFkZXItbGVmdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQouaGVhZGVyLWxlZnQgLmVsLWljb24tYXJyb3ctcmlnaHQgew0KICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zczsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmhlYWRlci1sZWZ0IC5lbC1pY29uLWFycm93LXJpZ2h0LmlzLWV4cGFuZGVkIHsNCiAgdHJhbnNmb3JtOiByb3RhdGUoOTBkZWcpOw0KfQ0KDQoucHJvZ3Jlc3MtdGltZWxpbmUgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZzogMTVweCAwOw0KfQ0KDQoudGltZWxpbmUtc2Nyb2xsIHsNCiAgbWF4LWhlaWdodDogMjAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBhZGRpbmc6IDAgMTVweDsNCn0NCg0KLnRpbWVsaW5lLXNjcm9sbDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNHB4Ow0KfQ0KDQoudGltZWxpbmUtc2Nyb2xsOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNjMGM0Y2M7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLnRpbWVsaW5lLXNjcm9sbDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoucHJvZ3Jlc3MtaXRlbSB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZzogOHB4IDAgOHB4IDIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLnByb2dyZXNzLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5wcm9ncmVzcy1kb3Qgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIGxlZnQ6IDA7DQogIHRvcDogMTJweDsNCiAgd2lkdGg6IDEwcHg7DQogIGhlaWdodDogMTBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTBlMGUwOw0KICBmbGV4LXNocmluazogMDsNCn0NCg0KLnByb2dyZXNzLWxpbmUgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIGxlZnQ6IDRweDsNCiAgdG9wOiAyMnB4Ow0KICBib3R0b206IC04cHg7DQogIHdpZHRoOiAycHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNlMGUwZTA7DQp9DQoNCi5wcm9ncmVzcy1jb250ZW50IHsNCiAgZmxleDogMTsNCiAgbWluLXdpZHRoOiAwOw0KfQ0KDQoucHJvZ3Jlc3MtdGltZSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCn0NCg0KLnByb2dyZXNzLXRleHQgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQp9DQoNCi5wcm9ncmVzcy1pdGVtLmNvbXBsZXRlZCAucHJvZ3Jlc3MtZG90IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzY3YzIzYTsNCn0NCg0KLnByb2dyZXNzLWl0ZW0uY29tcGxldGVkIC5wcm9ncmVzcy1saW5lIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzY3YzIzYTsNCn0NCg0KLnByb2dyZXNzLWl0ZW0uY3VycmVudCAucHJvZ3Jlc3MtZG90IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzQwOWVmZjsNCiAgYW5pbWF0aW9uOiBwdWxzZSAxLjVzIGluZmluaXRlOw0KfQ0KDQoucHJvZ3Jlc3MtaXRlbS5jdXJyZW50IC5wcm9ncmVzcy1saW5lIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzQwOWVmZjsNCn0NCg0KLmFpLW5hbWUgew0KICBmb250LXdlaWdodDogNjAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouaGVhZGVyLXJpZ2h0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5zdGF0dXMtdGV4dCB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi5zdGF0dXMtaWNvbiB7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLnN1Y2Nlc3MtaWNvbiB7DQogIGNvbG9yOiAjNjdjMjNhOw0KfQ0KDQouZXJyb3ItaWNvbiB7DQogIGNvbG9yOiAjZjU2YzZjOw0KfQ0KDQpAa2V5ZnJhbWVzIHB1bHNlIHsNCiAgMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSg2NCwgMTU4LCAyNTUsIDAuNCk7DQogIH0NCiAgNzAlIHsNCiAgICBib3gtc2hhZG93OiAwIDAgMCA2cHggcmdiYSg2NCwgMTU4LCAyNTUsIDApOw0KICB9DQogIDEwMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSg2NCwgMTU4LCAyNTUsIDApOw0KICB9DQp9DQoNCi5zY3JlZW5zaG90LWltYWdlIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zczsNCn0NCg0KLnNjcmVlbnNob3QtaW1hZ2U6aG92ZXIgew0KICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KfQ0KDQoucmVzdWx0cy1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgcGFkZGluZzogMCAxMHB4Ow0KfQ0KDQoucmVzdWx0LWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4IDMwcHg7DQp9DQoNCi5yZXN1bHQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5yZXN1bHQtdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoucmVzdWx0LWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEwcHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5zaGFyZS1saW5rLWJ0biwNCi5wdXNoLW1lZGlhLWJ0biB7DQogIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogIHBhZGRpbmc6IDZweCAxMnB4Ow0KfQ0KDQoubWFya2Rvd24tY29udGVudCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIG1heC1oZWlnaHQ6IDQwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCn0NCg0KLmFjdGlvbi1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgZ2FwOiAxMHB4Ow0KICBwYWRkaW5nOiAwIDEwcHg7DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsNCiAgLmFpLWNhcmQgew0KICAgIHdpZHRoOiBjYWxjKDMzLjMzJSAtIDE0cHgpOw0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkgew0KICAuYWktY2FyZCB7DQogICAgd2lkdGg6IGNhbGMoNTAlIC0gMTBweCk7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5haS1jYXJkIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0KDQouZWwtY29sbGFwc2Ugew0KICBib3JkZXItdG9wOiBub25lOw0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQouZWwtY29sbGFwc2UtaXRlbV9fY29udGVudCB7DQogIHBhZGRpbmc6IDE1cHggMDsNCn0NCg0KLmFpLXNlbGVjdGlvbi1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLnByb21wdC1pbnB1dC1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCiAgcGFkZGluZzogMCAyMHB4IDAgMHB4Ow0KfQ0KDQouaW1hZ2UtZGlhbG9nIC5lbC1kaWFsb2dfX2JvZHkgew0KICBwYWRkaW5nOiAwOw0KfQ0KDQoubGFyZ2UtaW1hZ2UtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICMwMDA7DQp9DQoNCi5sYXJnZS1pbWFnZSB7DQogIG1heC13aWR0aDogMTAwJTsNCiAgbWF4LWhlaWdodDogODB2aDsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCn0NCg0KLmltYWdlLWRpYWxvZyAuZWwtY2Fyb3VzZWwgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQouaW1hZ2UtZGlhbG9nIC5lbC1jYXJvdXNlbF9fY29udGFpbmVyIHsNCiAgaGVpZ2h0OiA4MHZoOw0KfQ0KDQouaW1hZ2UtZGlhbG9nIC5lbC1jYXJvdXNlbF9faXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwOw0KfQ0KDQouc2VjdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5zY29yZS1kaWFsb2ctY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5zZWxlY3RlZC1yZXN1bHRzIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnJlc3VsdC1jaGVja2JveCB7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnNjb3JlLXByb21wdC1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLnNjb3JlLXByb21wdC1pbnB1dCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoNCi5zY29yZS1wcm9tcHQtaW5wdXQgLmVsLXRleHRhcmVhX19pbm5lciB7DQogIG1pbi1oZWlnaHQ6IDUwMHB4ICFpbXBvcnRhbnQ7DQp9DQoNCi5kaWFsb2ctZm9vdGVyIHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQp9DQoNCi5zY29yZS1kaWFsb2cgLmVsLWRpYWxvZyB7DQogIGhlaWdodDogOTV2aDsNCiAgbWFyZ2luLXRvcDogMi41dmggIWltcG9ydGFudDsNCn0NCg0KLnNjb3JlLWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsNCiAgaGVpZ2h0OiBjYWxjKDk1dmggLSAxMjBweCk7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5sYXlvdXQtZGlhbG9nLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQoubGF5b3V0LXByb21wdC1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCn0NCg0KLmxheW91dC1wcm9tcHQtaW5wdXQgew0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KfQ0KDQoubGF5b3V0LXByb21wdC1pbnB1dCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgbWluLWhlaWdodDogNTAwcHggIWltcG9ydGFudDsNCn0NCg0KLmxheW91dC1kaWFsb2cgLmVsLWRpYWxvZyB7DQogIGhlaWdodDogOTV2aDsNCiAgbWFyZ2luLXRvcDogMi41dmggIWltcG9ydGFudDsNCn0NCg0KLmxheW91dC1kaWFsb2cgLmVsLWRpYWxvZ19fYm9keSB7DQogIGhlaWdodDogY2FsYyg5NXZoIC0gMTIwcHgpOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQoubmF2LWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDIwcHg7DQp9DQoNCi5oaXN0b3J5LWJ1dHRvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5oaXN0b3J5LWljb24gew0KICB3aWR0aDogMjRweDsNCiAgaGVpZ2h0OiAyNHB4Ow0KICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KfQ0KDQouaGlzdG9yeS1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLmhpc3RvcnktZ3JvdXAgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouaGlzdG9yeS1kYXRlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgcGFkZGluZzogNXB4IDA7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQouaGlzdG9yeS1saXN0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgZ2FwOiAxMHB4Ow0KfQ0KDQouaGlzdG9yeS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouaGlzdG9yeS1wYXJlbnQgew0KICBwYWRkaW5nOiAxMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zczsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5oaXN0b3J5LXBhcmVudDpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmY7DQp9DQoNCi5oaXN0b3J5LWNoaWxkcmVuIHsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQouaGlzdG9yeS1jaGlsZC1pdGVtIHsNCiAgcGFkZGluZzogOHB4IDEwcHg7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLmhpc3RvcnktY2hpbGQtaXRlbTpsYXN0LWNoaWxkIHsNCiAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCn0NCg0KLmhpc3RvcnktY2hpbGQtaXRlbTpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi5oaXN0b3J5LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICBnYXA6IDhweDsNCn0NCg0KLmhpc3RvcnktaGVhZGVyIC5lbC1pY29uLWFycm93LXJpZ2h0IHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgbWFyZ2luLXRvcDogM3B4Ow0KfQ0KDQouaGlzdG9yeS1oZWFkZXIgLmVsLWljb24tYXJyb3ctcmlnaHQuaXMtZXhwYW5kZWQgew0KICB0cmFuc2Zvcm06IHJvdGF0ZSg5MGRlZyk7DQp9DQoNCi5oaXN0b3J5LXByb21wdCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIG1hcmdpbi1ib3R0b206IDVweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7DQogIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogIGZsZXg6IDE7DQp9DQoNCi5oaXN0b3J5LXRpbWUgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouY2FwYWJpbGl0eS1idXR0b24gew0KICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCn0NCg0KLmNhcGFiaWxpdHktYnV0dG9uLmVsLWJ1dHRvbi0tcHJpbWFyeSB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7DQogIGJvcmRlci1jb2xvcjogIzQwOWVmZjsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi5jYXBhYmlsaXR5LWJ1dHRvbi5lbC1idXR0b24tLWluZm8gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItY29sb3I6ICNkY2RmZTY7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouY2FwYWJpbGl0eS1idXR0b24uZWwtYnV0dG9uLS1pbmZvOmhvdmVyIHsNCiAgY29sb3I6ICM0MDllZmY7DQogIGJvcmRlci1jb2xvcjogI2M2ZTJmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2VjZjVmZjsNCn0NCg0KLmNhcGFiaWxpdHktYnV0dG9uLmVsLWJ1dHRvbi0tcHJpbWFyeTpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICM2NmIxZmY7DQogIGJvcmRlci1jb2xvcjogIzY2YjFmZjsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi8qIOWIhuS6q+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwjEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\" />\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img\r\n              :src=\"require('../../../assets/ai/celan.png')\"\r\n              alt=\"历史记录\"\r\n              class=\"history-icon\"\r\n            />\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer\r\n      title=\"历史会话记录\"\r\n      :visible.sync=\"historyDrawerVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\"\r\n    >\r\n      <div class=\"history-content\">\r\n        <div\r\n          v-for=\"(group, date) in groupedHistory\"\r\n          :key=\"date\"\r\n          class=\"history-group\"\r\n        >\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div\r\n              v-for=\"(item, index) in group\"\r\n              :key=\"index\"\r\n              class=\"history-item\"\r\n            >\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i\r\n                    :class=\"[\r\n                      'el-icon-arrow-right',\r\n                      { 'is-expanded': item.isExpanded },\r\n                    ]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"\r\n                  ></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">\r\n                  {{ formatHistoryTime(item.createTime) }}\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-if=\"\r\n                  item.children && item.children.length > 0 && item.isExpanded\r\n                \"\r\n                class=\"history-children\"\r\n              >\r\n                <div\r\n                  v-for=\"(child, childIndex) in item.children\"\r\n                  :key=\"childIndex\"\r\n                  class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\"\r\n                >\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">\r\n                    {{ formatHistoryTime(child.createTime) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card\r\n                v-for=\"(ai, index) in aiList\"\r\n                :key=\"index\"\r\n                class=\"ai-card\"\r\n                shadow=\"hover\"\r\n              >\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\" />\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch\r\n                      v-model=\"ai.enabled\"\r\n                      active-color=\"#13ce66\"\r\n                      inactive-color=\"#ff4949\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <!-- 通义只支持单选-->\r\n                  <div v-if=\"ai.name === '通义千问'\" class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapability === capability.value ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"ai.selectedCapability !== capability.value\"\r\n                      @click=\"selectSingleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                  <!-- 其他AI -->\r\n                  <div v-else class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\"\r\n                      size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入提示词，支持Markdown格式\"\r\n              v-model=\"promptInput\"\r\n              resize=\"none\"\r\n              class=\"prompt-input\"\r\n            >\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"sendPrompt\"\r\n                :disabled=\"!canSend\"\r\n                class=\"send-button\"\r\n              >\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div\r\n                  v-for=\"(ai, index) in enabledAIs\"\r\n                  :key=\"index\"\r\n                  class=\"task-item\"\r\n                >\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i\r\n                        :class=\"[\r\n                          'el-icon-arrow-right',\r\n                          { 'is-expanded': ai.isExpanded },\r\n                        ]\"\r\n                      ></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{\r\n                        getStatusText(ai.status)\r\n                      }}</span>\r\n                      <i\r\n                        :class=\"getStatusIcon(ai.status)\"\r\n                        class=\"status-icon\"\r\n                      ></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div\r\n                    class=\"progress-timeline\"\r\n                    v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\"\r\n                  >\r\n                    <div class=\"timeline-scroll\">\r\n                      <div\r\n                        v-for=\"(log, logIndex) in ai.progressLogs\"\r\n                        :key=\"logIndex\"\r\n                        class=\"progress-item\"\r\n                        :class=\"{\r\n                          completed: log.isCompleted || logIndex > 0,\r\n                          current: !log.isCompleted && logIndex === 0,\r\n                        }\"\r\n                      >\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div\r\n                          class=\"progress-line\"\r\n                          v-if=\"logIndex < ai.progressLogs.length - 1\"\r\n                        ></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">\r\n                            {{ formatTime(log.timestamp) }}\r\n                          </div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch\r\n                    v-model=\"autoPlay\"\r\n                    active-text=\"自动轮播\"\r\n                    inactive-text=\"手动切换\"\r\n                  >\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel\r\n                  :interval=\"3000\"\r\n                  :autoplay=\"false\"\r\n                  indicator-position=\"outside\"\r\n                  height=\"700px\"\r\n                >\r\n                  <el-carousel-item\r\n                    v-for=\"(screenshot, index) in screenshots\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <img\r\n                      :src=\"screenshot\"\r\n                      alt=\"执行截图\"\r\n                      class=\"screenshot-image\"\r\n                      @click=\"showLargeImage(screenshot)\"\r\n                    />\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane\r\n            v-for=\"(result, index) in results\"\r\n            :key=\"index\"\r\n            :label=\"result.aiName\"\r\n            :name=\"'result-' + index\"\r\n          >\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-link\"\r\n                    @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\"\r\n                  >\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"success\"\r\n                    icon=\"el-icon-s-promotion\"\r\n                    @click=\"handlePushToMedia(result)\"\r\n                    class=\"push-media-btn\"\r\n                    :loading=\"pushingToMedia\"\r\n                    :disabled=\"pushingToMedia\"\r\n                  >\r\n                    投递到媒体\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img\r\n                  v-if=\"isImageFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  alt=\"分享图片\"\r\n                  class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\"\r\n                />\r\n                <!-- 渲染PDF -->\r\n                <iframe\r\n                  v-else-if=\"isPdfFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  class=\"share-pdf\"\r\n                  frameborder=\"0\"\r\n                >\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"openShareUrl(result.shareImgUrl)\"\r\n                  >\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"markdown-content\"\r\n                v-html=\"renderMarkdown(result.content)\"\r\n              ></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  @click=\"copyResult(result.content)\"\r\n                  >复制（纯文本）</el-button\r\n                >\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"success\"\r\n                  @click=\"exportResult(result)\"\r\n                  >导出（MD文件）</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"showImageDialog\"\r\n      width=\"90%\"\r\n      :show-close=\"true\"\r\n      :modal=\"true\"\r\n      center\r\n      class=\"image-dialog\"\r\n      :append-to-body=\"true\"\r\n      @close=\"closeLargeImage\"\r\n    >\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div\r\n          v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\"\r\n          class=\"single-image-container\"\r\n        >\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\" />\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel\r\n          v-else\r\n          :interval=\"3000\"\r\n          :autoplay=\"false\"\r\n          indicator-position=\"outside\"\r\n          height=\"80vh\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(screenshot, index) in screenshots\"\r\n            :key=\"index\"\r\n          >\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\" />\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog\r\n      title=\"智能评分\"\r\n      :visible.sync=\"scoreDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\"\r\n    >\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"10\"\r\n            placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\"\r\n            v-model=\"scorePrompt\"\r\n            resize=\"none\"\r\n            class=\"score-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox\r\n              v-for=\"(result, index) in results\"\r\n              :key=\"index\"\r\n              :label=\"result.aiName\"\r\n              class=\"result-checkbox\"\r\n            >\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 投递到媒体弹窗 -->\r\n    <el-dialog\r\n      title=\"媒体投递设置\"\r\n      :visible.sync=\"layoutDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"layout-dialog\"\r\n    >\r\n      <div class=\"layout-dialog-content\">\r\n        <!-- 媒体选择区域 -->\r\n        <div class=\"media-selection-section\">\r\n          <h3>选择投递媒体：</h3>\r\n          <el-radio-group v-model=\"selectedMedia\" size=\"small\" class=\"media-radio-group\">\r\n            <el-radio-button label=\"wechat\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              公众号\r\n            </el-radio-button>\r\n            <el-radio-button label=\"zhihu\">\r\n              <i class=\"el-icon-document\"></i>\r\n              知乎\r\n            </el-radio-button>\r\n            <el-radio-button label=\"toutiao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              微头条\r\n            </el-radio-button>\r\n            <el-radio-button label=\"baijiahao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              百家号\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n          <div class=\"media-description\">\r\n            <template v-if=\"selectedMedia === 'wechat'\">\r\n              <small>📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'zhihu'\">\r\n              <small>📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>📰 将内容转换为微头条文章格式，支持文章编辑和发布</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>🔈 将内容转换为百家号帖子格式，直接投递到百家号草稿箱</small>\r\n            </template>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"layout-prompt-section\">\r\n          <h3>排版提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"12\"\r\n            placeholder=\"请输入排版提示词\"\r\n            v-model=\"layoutPrompt\"\r\n            resize=\"none\"\r\n            class=\"layout-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"layoutDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleLayout\" :disabled=\"!canLayout\">\r\n          排版后智能投递\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条发布流程弹窗 -->\r\n    <el-dialog title=\"微头条发布流程\" :visible.sync=\"tthFlowVisible\" width=\"60%\" height=\"60%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-flow-dialog\">\r\n      <div class=\"tth-flow-content\">\r\n        <div class=\"flow-logs-section\">\r\n          <h3>发布流程日志：</h3>\r\n          <div class=\"progress-timeline\">\r\n            <div class=\"timeline-scroll\">\r\n              <div v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" class=\"progress-item completed\">\r\n                <div class=\"progress-dot\"></div>\r\n                <div v-if=\"index < tthFlowLogs.length - 1\" class=\"progress-line\"></div>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                  <div class=\"progress-text\">{{ log.content }}</div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"tthFlowLogs.length === 0\" class=\"no-logs\">暂无流程日志...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flow-images-section\">\r\n          <h3>发布流程图片：</h3>\r\n          <div class=\"flow-images-container\">\r\n            <template v-if=\"tthFlowImages.length > 0\">\r\n              <div v-for=\"(image, index) in tthFlowImages\" :key=\"index\" class=\"flow-image-item\">\r\n                <img :src=\"image\" alt=\"流程图片\" class=\"flow-image\" @click=\"showLargeImage(image)\">\r\n              </div>\r\n            </template>\r\n            <div v-else class=\"no-logs\">暂无流程图片...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeTTHFlowDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条文章编辑弹窗 -->\r\n    <el-dialog title=\"微头条文章编辑\" :visible.sync=\"tthArticleEditVisible\" width=\"70%\" height=\"80%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-article-edit-dialog\">\r\n      <div class=\"tth-article-edit-content\">\r\n        <div class=\"article-title-section\">\r\n          <h3>文章标题：</h3>\r\n          <el-input v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" class=\"article-title-input\"></el-input>\r\n        </div>\r\n        <div class=\"article-content-section\">\r\n          <h3>文章内容：</h3>\r\n          <div class=\"content-input-wrapper\">\r\n            <el-input \r\n              type=\"textarea\" \r\n              v-model=\"tthArticleContent\" \r\n              :rows=\"20\" \r\n              placeholder=\"请输入文章内容\"\r\n              resize=\"none\" \r\n              class=\"article-content-input\"\r\n              :class=\"{ 'content-over-limit': tthArticleContent.length > 2000 }\"\r\n            ></el-input>\r\n            <div class=\"content-length-info\" :class=\"{ 'text-danger': tthArticleContent.length > 2000 }\">\r\n              字数：{{ tthArticleContent.length }}/2000\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"tthArticleEditVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmTTHPublish\" :disabled=\"!tthArticleTitle || !tthArticleContent\">\r\n          确定发布\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { marked } from \"marked\";\r\nimport {\r\n  message,\r\n  saveUserChatData,\r\n  getChatHistory,\r\n  pushAutoOffice,\r\n  getMediaCallWord,\r\n} from \"@/api/wechat/aigc\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport websocketClient from \"@/utils/websocket\";\r\nimport store from \"@/store\";\r\nimport TurndownService from \"turndown\";\r\n\r\nexport default {\r\n  name: \"AIManagementPlatform\",\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: \"\",\r\n        userId: \"\",\r\n        corpId: \"\",\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        isNewChat: true,\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"\",\r\n        params: {},\r\n      },\r\n      aiList: [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: \"\",\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: \"result-0\",\r\n      activeCollapses: [\"ai-selection\", \"prompt-input\"], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: \"\",\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: \"atx\",\r\n        codeBlockStyle: \"fenced\",\r\n        emDelimiter: \"*\",\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇内容，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      layoutDialogVisible: false,\r\n      layoutPrompt: \"\",\r\n      currentLayoutResult: null, // 当前要排版的结果\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n      selectedMedia: \"wechat\", // 默认选择公众号\r\n      pushingToMedia: false, // 投递到媒体的loading状态\r\n      // 微头条相关变量\r\n      tthFlowVisible: false, // 微头条发布流程弹窗\r\n      tthFlowLogs: [], // 微头条发布流程日志\r\n      tthFlowImages: [], // 微头条发布流程图片\r\n      tthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n      tthArticleTitle: '', // 微头条文章标题\r\n      tthArticleContent: '', // 微头条文章内容\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return (\r\n        this.promptInput.trim().length > 0 &&\r\n        this.aiList.some((ai) => ai.enabled)\r\n      );\r\n    },\r\n    canScore() {\r\n      return (\r\n        this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0\r\n      );\r\n    },\r\n    canLayout() {\r\n      return this.layoutPrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach((item) => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach((chatGroup) => {\r\n        // 按时间排序\r\n        chatGroup.sort(\r\n          (a, b) => new Date(a.createTime) - new Date(b.createTime)\r\n        );\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map((child) => ({\r\n            ...child,\r\n            isParent: false,\r\n          })),\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    },\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  watch: {\r\n    // 监听媒体选择变化，自动加载对应的提示词\r\n    selectedMedia: {\r\n      handler(newMedia) {\r\n        this.loadMediaPrompt(newMedia);\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots = [];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = \"\";\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter((ai) => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach((ai) => {\r\n        this.$set(ai, \"status\", \"running\");\r\n      });\r\n\r\n      this.enabledAIs.forEach((ai) => {\r\n        if (ai.name === \"DeepSeek\" && ai.enabled) {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"deepseek,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-lwss,\";\r\n          }\r\n        }\r\n        if (ai.name === \"豆包\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db-sdsk,\";\r\n          }\r\n        }\r\n        if (ai.name === \"MiniMax Chat\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n          }\r\n        }\r\n        if(ai.name === '通义千问' && ai.enabled){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n          if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n          } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq);\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\";\r\n      this.jsonRpcReqest.params = this.userInfoReq;\r\n      this.message(this.jsonRpcReqest);\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then((res) => {\r\n        if (res.code == 201) {\r\n          this.$message.error(res.messages || '操作失败');\r\n        }\r\n      });\r\n    },\r\n    // 处理通义单选逻辑\r\n    selectSingleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      if (ai.selectedCapability === capabilityValue) {\r\n        this.$set(ai, 'selectedCapability', '');\r\n      } else {\r\n        this.$set(ai, 'selectedCapability', capabilityValue);\r\n      }\r\n      this.$forceUpdate();\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log(\"切换前:\", ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(\r\n          ai.selectedCapabilities,\r\n          ai.selectedCapabilities.length,\r\n          capabilityValue\r\n        );\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, \"selectedCapabilities\", newCapabilities);\r\n      }\r\n      console.log(\"切换后:\", ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"等待中\";\r\n        case \"running\":\r\n          return \"正在执行\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"failed\":\r\n          return \"执行失败\";\r\n        default:\r\n          return \"未知状态\";\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"el-icon-time\";\r\n        case \"running\":\r\n          return \"el-icon-loading\";\r\n        case \"completed\":\r\n          return \"el-icon-check success-icon\";\r\n        case \"failed\":\r\n          return \"el-icon-close error-icon\";\r\n        default:\r\n          return \"el-icon-question\";\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement(\"div\");\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || \"\";\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement(\"textarea\");\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textarea);\r\n      this.$message.success(\"已复制纯文本到剪贴板\");\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: \"text/markdown\" });\r\n      const link = document.createElement(\"a\");\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date()\r\n        .toISOString()\r\n        .slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success(\"已导出Markdown文件\");\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, \"_blank\");\r\n      } else {\r\n        this.$message.warning(\"暂无原链接\");\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector(\".image-dialog .el-carousel\");\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = \"\";\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log(\"WebSocket URL:\", process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case \"open\":\r\n            // this.$message.success('');\r\n            break;\r\n          case \"message\":\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case \"close\":\r\n            this.$message.warning(\"WebSocket连接已关闭\");\r\n            break;\r\n          case \"error\":\r\n            this.$message.error(\"WebSocket连接错误\");\r\n            break;\r\n          case \"reconnect_failed\":\r\n            this.$message.error(\"WebSocket重连失败，请刷新页面重试\");\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === \"RETURN_YBT1_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_YBDS_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_DB_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.tyChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.maxChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_LOG\" && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(\r\n          (ai) => ai.name === dataObj.aiName\r\n        );\r\n        if (targetAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = targetAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            targetAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到知乎\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            zhihuAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到百家号\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            baijiahaoAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理截图消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_IMG\" && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n      // 处理智能评分结果\r\n      if (dataObj.type === \"RETURN_WKPF_RES\") {\r\n        const wkpfAI = this.enabledAIs.find((ai) => ai.name === \"智能评分\");\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, \"status\", \"completed\");\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: \"智能评分\",\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能排版结果\r\n      if (dataObj.type === \"RETURN_ZNPB_RES\") {\r\n        const znpbAI = this.enabledAIs.find((ai) => ai.name === \"智能排版\");\r\n        if (znpbAI) {\r\n          this.$set(znpbAI, \"status\", \"completed\");\r\n          if (znpbAI.progressLogs.length > 0) {\r\n            this.$set(znpbAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 直接调用投递到公众号的方法，不添加到结果展示\r\n          this.pushToWechatWithContent(dataObj.draftContent);\r\n\r\n          // 智能排版完成时，保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_ZHIHU_DELIVERY_RES\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          this.$set(zhihuAI, \"status\", \"completed\");\r\n          if (zhihuAI.progressLogs.length > 0) {\r\n            this.$set(zhihuAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          zhihuAI.progressLogs.unshift({\r\n            content: \"知乎投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 知乎投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"知乎投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_BAIJIAHAO_DELIVERY_RES\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          this.$set(baijiahaoAI, \"status\", \"completed\");\r\n          if (baijiahaoAI.progressLogs.length > 0) {\r\n            this.$set(baijiahaoAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          baijiahaoAI.progressLogs.unshift({\r\n            content: \"百家号投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 百家号投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"百家号投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理微头条排版结果\r\n      if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n        // 微头条排版AI节点状态设为已完成\r\n        const tthpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n        if (tthpbAI) {\r\n          this.$set(tthpbAI, 'status', 'completed');\r\n          if (tthpbAI.progressLogs.length > 0) {\r\n            this.$set(tthpbAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n        }\r\n        this.tthArticleTitle = dataObj.title || '';\r\n        this.tthArticleContent = dataObj.content || '';\r\n        this.tthArticleEditVisible = true;\r\n        this.saveHistory();\r\n        return;\r\n      }\r\n\r\n      // 处理微头条发布流程\r\n      if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n        // 添加流程日志\r\n        if (dataObj.content) {\r\n          this.tthFlowLogs.push({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            type: 'flow',\r\n          });\r\n        }\r\n        // 处理图片信息\r\n        if (dataObj.shareImgUrl) {\r\n          this.tthFlowImages.push(dataObj.shareImgUrl);\r\n        }\r\n        // 确保流程弹窗显示\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        // 检查发布结果\r\n        if (dataObj.content === 'success') {\r\n          this.$message.success('发布到微头条成功！');\r\n          this.tthFlowVisible = true;\r\n        } else if (dataObj.content === 'false' || dataObj.content === false) {\r\n          this.$message.error('发布到微头条失败！');\r\n          this.tthFlowVisible = false;\r\n          this.tthArticleEditVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 兼容后端发送的RETURN_PC_TTH_IMG类型图片消息\r\n      if (dataObj.type === 'RETURN_PC_TTH_IMG' && dataObj.url) {\r\n        this.tthFlowImages.push(dataObj.url);\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case \"RETURN_YBT1_RES\":\r\n        case \"RETURN_TURBOS_RES\":\r\n        case \"RETURN_TURBOS_LARGE_RES\":\r\n        case \"RETURN_DEEPSEEK_RES\":\r\n          console.log(\"收到DeepSeek消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"DeepSeek\");\r\n          break;\r\n        case \"RETURN_YBDS_RES\":\r\n        case \"RETURN_DB_RES\":\r\n          console.log(\"收到豆包消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"豆包\");\r\n          break;\r\n        case \"RETURN_MAX_RES\":\r\n          console.log(\"收到MiniMax消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n          break;\r\n        case 'RETURN_TY_RES':\r\n          console.log('收到通义千问消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n          break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, \"status\", \"completed\");\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], \"isCompleted\", true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(\r\n          (r) => r.aiName === targetAI.name\r\n        );\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error(\"WebSocket未连接\");\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, \"isExpanded\", !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter((result) => this.selectedResults.includes(result.aiName))\r\n        .map((result) => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join(\"\\n\");\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"AI评分\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: \"zj-db-sdsk\", // 默认使用豆包进行评分\r\n        },\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest);\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: \"智能评分\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"智能评分任务已提交，正在评分...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"智能评分\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"智能评分\"\r\n      );\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"评分请求已发送，请等待结果\");\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return \"今天\";\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return \"昨天\";\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return \"两天前\";\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return \"三天前\";\r\n      } else {\r\n        return date.toLocaleDateString(\"zh-CN\", {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || \"\";\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || \"\";\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || \"\";\r\n        this.userInfoReq.dbChatId = item.dbChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.tyChatId || \"\";\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success(\"历史记录加载成功\");\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId,\r\n        tyChatId: this.userInfoReq.tyChatId,\r\n        maxChatId: this.userInfoReq.maxChatId,\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n          maxChatId: this.userInfoReq.maxChatId,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"保存历史记录失败:\", error);\r\n        this.$message.error(\"保存历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(\r\n        this.expandedHistoryItems,\r\n        item.chatId,\r\n        !this.expandedHistoryItems[item.chatId]\r\n      );\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = \"\";\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: \"\",\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        maxChatId: \"\",\r\n        isNewChat: true,\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n\r\n      this.$message.success(\"已创建新对话\");\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId, 0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载上次会话失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = [\r\n        \".jpg\",\r\n        \".jpeg\",\r\n        \".png\",\r\n        \".gif\",\r\n        \".bmp\",\r\n        \".webp\",\r\n        \".svg\",\r\n      ];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some((ext) => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes(\".pdf\");\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        DeepSeek: \"700px\",\r\n        豆包: \"560px\",\r\n        通义千问: \"700px\",\r\n      };\r\n\r\n      const width = widthMap[aiName] || \"560px\"; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: \"auto\",\r\n      };\r\n    },\r\n\r\n    // 投递到媒体\r\n    handlePushToMedia(result) {\r\n      this.currentLayoutResult = result;\r\n      this.showLayoutDialog(result);\r\n    },\r\n\r\n    // 显示智能排版对话框\r\n    showLayoutDialog(result) {\r\n      this.currentLayoutResult = result;\r\n      this.layoutDialogVisible = true;\r\n      // 加载当前选择媒体的提示词\r\n      this.loadMediaPrompt(this.selectedMedia);\r\n    },\r\n\r\n    // 加载媒体提示词\r\n    async loadMediaPrompt(media) {\r\n      if (!media) return;\r\n\r\n      let platformId;\r\n      if(media === 'wechat'){\r\n        platformId = 'wechat_layout';\r\n      }else if(media === 'zhihu'){\r\n        platformId = 'zhihu_layout';\r\n      }else if(media === 'baijiahao'){\r\n        platformId = 'baijiahao_layout';\r\n      }else if(media === 'toutiao'){\r\n        platformId = 'weitoutiao_layout';\r\n      }\r\n\r\n      try {\r\n        const response = await getMediaCallWord(platformId);\r\n        if (response.code === 200) {\r\n          this.layoutPrompt = response.data + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        } else {\r\n          // 使用默认提示词\r\n          this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载提示词失败:', error);\r\n        // 使用默认提示词\r\n        this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n      }\r\n    },\r\n\r\n    // 获取默认提示词(仅在后端访问失败时使用)\r\n    getDefaultPrompt(media) {\r\n      if (media === 'wechat') {\r\n        return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：`;\r\n      } else if (media === 'zhihu') {\r\n        return `请将以下内容整理为适合知乎发布的Markdown格式文章。要求：\r\n1. 保持内容的专业性和可读性\r\n2. 使用合适的标题层级（## ### #### 等）\r\n3. 代码块使用\\`\\`\\`标记，并指定语言类型\r\n4. 重要信息使用**加粗**标记\r\n5. 列表使用- 或1. 格式\r\n6. 删除不必要的格式标记\r\n7. 确保内容适合知乎的阅读习惯\r\n8. 文章结构清晰，逻辑连贯\r\n9. 目标是作为一篇专业文章投递到知乎草稿箱\r\n\r\n请对以下内容进行排版：`;\r\n\r\n      }else if (media === 'baijiahao') {\r\n        return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括\"一、\"，\"1.\"等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话`;\r\n\r\n      }else if (media === 'toutiao') {\r\n        return `根据智能评分内容，写一篇微头条文章，只能包含标题和内容，要求如下：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数严格控制在1000字以上，2000字以下\r\n7. 强制要求：只能回答标题和内容，标题必须用英文双引号（\"\"）引用起来，且放在首位，不能有其他多余的话\r\n8. 严格要求：AI必须严格遵守所有严格条件，不要输出其他多余的内容，只要标题和内容\r\n9. 内容不允许出现编号，要正常文章格式\r\n\r\n请对以下内容进行排版：`;\r\n      }\r\n      return '请对以下内容进行排版：';\r\n    },\r\n\r\n    // 处理智能排版\r\n    handleLayout() {\r\n      if (!this.canLayout || !this.currentLayoutResult) return;\r\n      this.layoutDialogVisible = false;\r\n\r\n      if (this.selectedMedia === 'zhihu') {\r\n        // 知乎投递：直接创建投递任务\r\n        this.createZhihuDeliveryTask();\r\n      } else if (this.selectedMedia === 'toutiao') {\r\n        // 微头条投递：创建微头条排版任务\r\n        this.createToutiaoLayoutTask();\r\n      } else if (this.selectedMedia === 'baijiahao') {\r\n        // 百家号投递：创建百家号排版任务\r\n        this.createBaijiahaoLayoutTask();\r\n      }else {\r\n        // 公众号投递：创建排版任务\r\n        this.createWechatLayoutTask();\r\n      }\r\n    },\r\n// 创建知乎投递任务（独立任务）\r\n    createZhihuDeliveryTask() {\r\n      const zhihuAI = {\r\n        name: \"投递到知乎\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"知乎投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到知乎\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在知乎投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到知乎\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(zhihuAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = zhihuAI;\r\n        const zhihu = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(zhihu);\r\n      }\r\n\r\n      // 发送知乎投递请求\r\n      const zhihuRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到知乎\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"zhihu\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"知乎投递参数\", zhihuRequest);\r\n      this.message(zhihuRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"知乎投递任务已创建，正在处理...\");\r\n    },\r\n    // 创建百家号投递任务（独立任务）\r\n    createBaijiahaoLayoutTask() {\r\n      const baijiahaoAI = {\r\n        name: \"投递到百家号\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"百家号投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到百家号\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在百家号投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到百家号\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = baijiahaoAI;\r\n        const baijiahao = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      }\r\n\r\n      // 发送百家号投递请求\r\n      const baijiahaoRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到百家号\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"baijiahao\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"百家号投递参数\", baijiahaoRequest);\r\n      this.message(baijiahaoRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"百家号投递任务已创建，正在处理...\");\r\n    },\r\n      // 创建公众号排版任务（保持原有逻辑）\r\n      createWechatLayoutTask() {\r\n        const layoutRequest = {\r\n          jsonrpc: \"2.0\",\r\n          id: uuidv4(),\r\n          method: \"AI排版\",\r\n          params: {\r\n            taskId: uuidv4(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: this.layoutPrompt,\r\n            roles: \"\",\r\n            selectedMedia: \"wechat\",\r\n          },\r\n        };\r\n\r\n        console.log(\"公众号排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        const znpbAI = {\r\n          name: \"智能排版\",\r\n          avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"running\",\r\n          progressLogs: [\r\n            {\r\n              content: \"智能排版任务已提交，正在排版...\",\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: \"智能排版\",\r\n            },\r\n          ],\r\n          isExpanded: true,\r\n        };\r\n\r\n        // 检查是否已存在智能排版任务\r\n        const existIndex = this.enabledAIs.findIndex(\r\n          (ai) => ai.name === \"智能排版\"\r\n        );\r\n        if (existIndex === -1) {\r\n          this.enabledAIs.unshift(znpbAI);\r\n        } else {\r\n          this.enabledAIs[existIndex] = znpbAI;\r\n          const znpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n          this.enabledAIs.unshift(znpb);\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.$message.success(\"排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 创建微头条排版任务\r\n    createToutiaoLayoutTask() {\r\n      // 获取智能评分内容\r\n      const scoreResult = this.results.find(r => r.aiName === '智能评分');\r\n      const scoreContent = scoreResult ? scoreResult.content : '';\r\n\r\n      const layoutRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"微头条排版\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: `${scoreContent}\\n${this.layoutPrompt}`,\r\n          roles: \"\",\r\n        },\r\n      };\r\n\r\n      console.log(\"微头条排版参数\", layoutRequest);\r\n      this.message(layoutRequest);\r\n\r\n      const tthpbAI = {\r\n        name: \"微头条排版\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"微头条排版任务已提交，正在排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"微头条排版\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在微头条排版任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"微头条排版\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(tthpbAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = tthpbAI;\r\n        const tthpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(tthpb);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"微头条排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 实际投递到公众号\r\n    pushToWechatWithContent(contentText) {\r\n      if (this.pushingToWechat) return;\r\n      this.$message.success(\"开始投递公众号！\");\r\n      this.pushingToWechat = true;\r\n      this.pushOfficeNum += 1;\r\n\r\n      const params = {\r\n        contentText: contentText,\r\n        shareUrl: this.currentLayoutResult.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: this.currentLayoutResult.aiName,\r\n      };\r\n\r\n      pushAutoOffice(params)\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.$message.success(\"投递到公众号成功！\");\r\n          } else {\r\n            this.$message.error(res.msg || \"投递失败，请重试\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"投递到公众号失败:\", error);\r\n          this.$message.error(\"投递失败，请重试\");\r\n        })\r\n        .finally(() => {\r\n          this.pushingToWechat = false;\r\n        });\r\n    },\r\n\r\n\r\n\r\n    // 确认微头条发布\r\n    confirmTTHPublish() {\r\n      if (!this.tthArticleTitle || !this.tthArticleContent) {\r\n        this.$message.warning('请填写标题和内容');\r\n        return;\r\n      }\r\n      // 构建微头条发布请求\r\n      const publishRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n                  method: '微头条发布',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          roles: '',\r\n          title: this.tthArticleTitle,\r\n          content: this.tthArticleContent,\r\n          type: '微头条发布'\r\n        }\r\n      };\r\n      // 发送发布请求\r\n      console.log(\"微头条发布参数\", publishRequest);\r\n      this.message(publishRequest);\r\n      this.tthArticleEditVisible = false;\r\n      // 显示流程弹窗\r\n      this.tthFlowVisible = true;\r\n      this.tthFlowLogs = [];\r\n      this.tthFlowImages = [];\r\n      this.$message.success('微头条发布请求已发送！');\r\n    },\r\n\r\n\r\n    // 关闭微头条发布流程弹窗\r\n    closeTTHFlowDialog() {\r\n      this.tthFlowVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card,\r\n.screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn,\r\n.push-media-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.layout-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.layout-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.layout-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.layout-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.layout-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.layout-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409eff;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 用于处理DeepSeek特殊格式的样式 */\r\n.deepseek-format-container {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 5px;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n/* DeepSeek响应内容的特定样式 */\r\n::v-deep .deepseek-response {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n::v-deep .deepseek-response pre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  display: block;\r\n  margin: 10px 0;\r\n}\r\n\r\n::v-deep .deepseek-response code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 4px;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n::v-deep .deepseek-response table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 15px 0;\r\n}\r\n\r\n::v-deep .deepseek-response th,\r\n::v-deep .deepseek-response td {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n::v-deep .deepseek-response th {\r\n  background-color: #f2f2f2;\r\n  font-weight: bold;\r\n}\r\n\r\n::v-deep .deepseek-response h1,\r\n::v-deep .deepseek-response h2,\r\n::v-deep .deepseek-response h3,\r\n::v-deep .deepseek-response h4,\r\n::v-deep .deepseek-response h5,\r\n::v-deep .deepseek-response h6 {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n  color: #222;\r\n}\r\n\r\n::v-deep .deepseek-response a {\r\n  color: #0066cc;\r\n  text-decoration: none;\r\n}\r\n\r\n::v-deep .deepseek-response blockquote {\r\n  border-left: 4px solid #ddd;\r\n  padding-left: 15px;\r\n  margin: 15px 0;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .deepseek-response ul,\r\n::v-deep .deepseek-response ol {\r\n  padding-left: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 媒体选择区域样式 */\r\n.media-selection-section {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.media-selection-section h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.media-radio-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner {\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner i {\r\n  font-size: 14px;\r\n}\r\n\r\n.media-description {\r\n  margin-top: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.media-description small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.layout-prompt-section h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 微头条相关样式 */\r\n.tth-flow-dialog {\r\n  .tth-flow-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 600px;\r\n  }\r\n\r\n  .flow-logs-section,\r\n  .flow-images-section {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .flow-logs-section h3,\r\n  .flow-images-section h3 {\r\n    margin: 0 0 12px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-timeline {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    padding: 12px;\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  .timeline-scroll {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .progress-item {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .progress-dot {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 4px;\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #67c23a;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .progress-line {\r\n    position: absolute;\r\n    left: 3px;\r\n    top: 12px;\r\n    width: 2px;\r\n    height: 20px;\r\n    background-color: #e4e7ed;\r\n  }\r\n\r\n  .progress-content {\r\n    .progress-time {\r\n      font-size: 12px;\r\n      color: #909399;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 13px;\r\n      color: #303133;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .flow-images-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n    max-height: 500px;\r\n  }\r\n\r\n  .flow-image-item {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .flow-image-item:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .flow-image {\r\n    max-width: 100%;\r\n    max-height: 400px;\r\n    min-height: 200px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    border: 2px solid #e4e7ed;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    object-fit: contain;\r\n  }\r\n\r\n  .flow-image:hover {\r\n    transform: scale(1.02);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    border-color: #409eff;\r\n  }\r\n\r\n  .no-logs {\r\n    text-align: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.tth-article-edit-dialog {\r\n  .tth-article-edit-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .article-title-section h3,\r\n  .article-content-section h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .article-title-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .article-content-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .content-input-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .content-length-info {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    z-index: 1;\r\n  }\r\n\r\n  .text-danger {\r\n    color: #f56c6c !important;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content-over-limit .el-textarea__inner {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}