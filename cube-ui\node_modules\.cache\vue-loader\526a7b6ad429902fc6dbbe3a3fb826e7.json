{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754098438104}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754057742816}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXJrZWQgfSBmcm9tICJtYXJrZWQiOw0KaW1wb3J0IHsNCiAgbWVzc2FnZSwNCiAgc2F2ZVVzZXJDaGF0RGF0YSwNCiAgZ2V0Q2hhdEhpc3RvcnksDQogIHB1c2hBdXRvT2ZmaWNlLA0KICBnZXRNZWRpYUNhbGxXb3JkLA0KfSBmcm9tICJAL2FwaS93ZWNoYXQvYWlnYyI7DQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICJ1dWlkIjsNCmltcG9ydCB3ZWJzb2NrZXRDbGllbnQgZnJvbSAiQC91dGlscy93ZWJzb2NrZXQiOw0KaW1wb3J0IHN0b3JlIGZyb20gIkAvc3RvcmUiOw0KaW1wb3J0IFR1cm5kb3duU2VydmljZSBmcm9tICJ0dXJuZG93biI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkFJTWFuYWdlbWVudFBsYXRmb3JtIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdXNlcklkOiBzdG9yZS5zdGF0ZS51c2VyLmlkLA0KICAgICAgY29ycElkOiBzdG9yZS5zdGF0ZS51c2VyLmNvcnBfaWQsDQogICAgICBjaGF0SWQ6IHV1aWR2NCgpLA0KICAgICAgZXhwYW5kZWRIaXN0b3J5SXRlbXM6IHt9LA0KICAgICAgdXNlckluZm9SZXE6IHsNCiAgICAgICAgdXNlclByb21wdDogIiIsDQogICAgICAgIHVzZXJJZDogIiIsDQogICAgICAgIGNvcnBJZDogIiIsDQogICAgICAgIHRhc2tJZDogIiIsDQogICAgICAgIHJvbGVzOiAiIiwNCiAgICAgICAgdG9uZUNoYXRJZDogIiIsDQogICAgICAgIHliRHNDaGF0SWQ6ICIiLA0KICAgICAgICBkYkNoYXRJZDogIiIsDQogICAgICAgIHR5Q2hhdElkOiAiIiwNCiAgICAgICAgaXNOZXdDaGF0OiB0cnVlLA0KICAgICAgfSwNCiAgICAgIGpzb25ScGNSZXFlc3Q6IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAiIiwNCiAgICAgICAgcGFyYW1zOiB7fSwNCiAgICAgIH0sDQogICAgICBhaUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICJEZWVwU2VlayIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvbG9nby9EZWVwc2Vlay5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICLmt7HluqbmgJ3ogIMiLCB2YWx1ZTogImRlZXBfdGhpbmtpbmciIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAi6IGU572R5pCc57SiIiwgdmFsdWU6ICJ3ZWJfc2VhcmNoIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsiZGVlcF90aGlua2luZyIsICJ3ZWJfc2VhcmNoIl0sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICJpZGxlIiwNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAi6LGG5YyFIiwNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9haS/osYbljIUucG5nIiksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbeyBsYWJlbDogIua3seW6puaAneiAgyIsIHZhbHVlOiAiZGVlcF90aGlua2luZyIgfV0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsiZGVlcF90aGlua2luZyJdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIk1pbmlNYXggQ2hhdCIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkvTWluaU1heC5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICLmt7HluqbmgJ3ogIMiLCB2YWx1ZTogImRlZXBfdGhpbmtpbmciIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAi6IGU572R5pCc57SiIiwgdmFsdWU6ICJ3ZWJfc2VhcmNoIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+mAmuS5ieWNg+mXricsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkvcXcucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+iBlOe9keaQnOe0oicsIHZhbHVlOiAnd2ViX3NlYXJjaCcgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXR5OiAnJywNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcHJvbXB0SW5wdXQ6ICIiLA0KICAgICAgdGFza1N0YXJ0ZWQ6IGZhbHNlLA0KICAgICAgYXV0b1BsYXk6IGZhbHNlLA0KICAgICAgc2NyZWVuc2hvdHM6IFtdLA0KICAgICAgcmVzdWx0czogW10sDQogICAgICBhY3RpdmVSZXN1bHRUYWI6ICJyZXN1bHQtMCIsDQogICAgICBhY3RpdmVDb2xsYXBzZXM6IFsiYWktc2VsZWN0aW9uIiwgInByb21wdC1pbnB1dCJdLCAvLyDpu5jorqTlsZXlvIDov5nkuKTkuKrljLrln58NCiAgICAgIHNob3dJbWFnZURpYWxvZzogZmFsc2UsDQogICAgICBjdXJyZW50TGFyZ2VJbWFnZTogIiIsDQogICAgICBlbmFibGVkQUlzOiBbXSwNCiAgICAgIHR1cm5kb3duU2VydmljZTogbmV3IFR1cm5kb3duU2VydmljZSh7DQogICAgICAgIGhlYWRpbmdTdHlsZTogImF0eCIsDQogICAgICAgIGNvZGVCbG9ja1N0eWxlOiAiZmVuY2VkIiwNCiAgICAgICAgZW1EZWxpbWl0ZXI6ICIqIiwNCiAgICAgIH0pLA0KICAgICAgc2NvcmVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNlbGVjdGVkUmVzdWx0czogW10sDQogICAgICBzY29yZVByb21wdDogYOivt+S9oOa3seW6pumYheivu+S7peS4i+WHoOevh+WGheWuue+8jOS7juWkmuS4que7tOW6pui/m+ihjOmAkOmhueaJk+WIhu+8jOi+k+WHuuivhOWIhue7k+aenOOAguW5tuWcqOS7peS4i+WQhOevh+aWh+eroOeahOWfuuehgOS4iuWNmumHh+S8l+mVv++8jOe7vOWQiOaVtOeQhuS4gOevh+abtOWFqOmdoueahOaWh+eroOOAgmAsDQogICAgICBsYXlvdXREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGxheW91dFByb21wdDogIiIsDQogICAgICBjdXJyZW50TGF5b3V0UmVzdWx0OiBudWxsLCAvLyDlvZPliY3opoHmjpLniYjnmoTnu5PmnpwNCiAgICAgIGhpc3RvcnlEcmF3ZXJWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoYXRIaXN0b3J5OiBbXSwNCiAgICAgIHB1c2hPZmZpY2VOdW06IDAsIC8vIOaKlemAkuWIsOWFrOS8l+WPt+eahOmAkuWinue8luWPtw0KICAgICAgcHVzaGluZ1RvV2VjaGF0OiBmYWxzZSwgLy8g5oqV6YCS5Yiw5YWs5LyX5Y+355qEbG9hZGluZ+eKtuaAgQ0KICAgICAgc2VsZWN0ZWRNZWRpYTogIndlY2hhdCIsIC8vIOm7mOiupOmAieaLqeWFrOS8l+WPtw0KICAgICAgcHVzaGluZ1RvTWVkaWE6IGZhbHNlLCAvLyDmipXpgJLliLDlqpLkvZPnmoRsb2FkaW5n54q25oCBDQogICAgICAvLyDlvq7lpLTmnaHnm7jlhbPlj5jph48NCiAgICAgIHR0aEZsb3dWaXNpYmxlOiBmYWxzZSwgLy8g5b6u5aS05p2h5Y+R5biD5rWB56iL5by556qXDQogICAgICB0dGhGbG93TG9nczogW10sIC8vIOW+ruWktOadoeWPkeW4g+a1geeoi+aXpeW/lw0KICAgICAgdHRoRmxvd0ltYWdlczogW10sIC8vIOW+ruWktOadoeWPkeW4g+a1geeoi+WbvueJhw0KICAgICAgdHRoQXJ0aWNsZUVkaXRWaXNpYmxlOiBmYWxzZSwgLy8g5b6u5aS05p2h5paH56ug57yW6L6R5by556qXDQogICAgICB0dGhBcnRpY2xlVGl0bGU6ICcnLCAvLyDlvq7lpLTmnaHmlofnq6DmoIfpopgNCiAgICAgIHR0aEFydGljbGVDb250ZW50OiAnJywgLy8g5b6u5aS05p2h5paH56ug5YaF5a65DQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBjYW5TZW5kKCkgew0KICAgICAgcmV0dXJuICgNCiAgICAgICAgdGhpcy5wcm9tcHRJbnB1dC50cmltKCkubGVuZ3RoID4gMCAmJg0KICAgICAgICB0aGlzLmFpTGlzdC5zb21lKChhaSkgPT4gYWkuZW5hYmxlZCkNCiAgICAgICk7DQogICAgfSwNCiAgICBjYW5TY29yZSgpIHsNCiAgICAgIHJldHVybiAoDQogICAgICAgIHRoaXMuc2VsZWN0ZWRSZXN1bHRzLmxlbmd0aCA+IDAgJiYgdGhpcy5zY29yZVByb21wdC50cmltKCkubGVuZ3RoID4gMA0KICAgICAgKTsNCiAgICB9LA0KICAgIGNhbkxheW91dCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmxheW91dFByb21wdC50cmltKCkubGVuZ3RoID4gMDsNCiAgICB9LA0KICAgIGdyb3VwZWRIaXN0b3J5KCkgew0KICAgICAgY29uc3QgZ3JvdXBzID0ge307DQogICAgICBjb25zdCBjaGF0R3JvdXBzID0ge307DQoNCiAgICAgIC8vIOmmluWFiOaMiWNoYXRJZOWIhue7hA0KICAgICAgdGhpcy5jaGF0SGlzdG9yeS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmICghY2hhdEdyb3Vwc1tpdGVtLmNoYXRJZF0pIHsNCiAgICAgICAgICBjaGF0R3JvdXBzW2l0ZW0uY2hhdElkXSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGNoYXRHcm91cHNbaXRlbS5jaGF0SWRdLnB1c2goaXRlbSk7DQogICAgICB9KTsNCg0KICAgICAgLy8g54S25ZCO5oyJ5pel5pyf5YiG57uE77yM5bm25aSE55CG54i25a2Q5YWz57O7DQogICAgICBPYmplY3QudmFsdWVzKGNoYXRHcm91cHMpLmZvckVhY2goKGNoYXRHcm91cCkgPT4gew0KICAgICAgICAvLyDmjInml7bpl7TmjpLluo8NCiAgICAgICAgY2hhdEdyb3VwLnNvcnQoDQogICAgICAgICAgKGEsIGIpID0+IG5ldyBEYXRlKGEuY3JlYXRlVGltZSkgLSBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpDQogICAgICAgICk7DQoNCiAgICAgICAgLy8g6I635Y+W5pyA5pep55qE6K6w5b2V5L2c5Li654i257qnDQogICAgICAgIGNvbnN0IHBhcmVudEl0ZW0gPSBjaGF0R3JvdXBbMF07DQogICAgICAgIGNvbnN0IGRhdGUgPSB0aGlzLmdldEhpc3RvcnlEYXRlKHBhcmVudEl0ZW0uY3JlYXRlVGltZSk7DQoNCiAgICAgICAgaWYgKCFncm91cHNbZGF0ZV0pIHsNCiAgICAgICAgICBncm91cHNbZGF0ZV0gPSBbXTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa3u+WKoOeItue6p+iusOW9lQ0KICAgICAgICBncm91cHNbZGF0ZV0ucHVzaCh7DQogICAgICAgICAgLi4ucGFyZW50SXRlbSwNCiAgICAgICAgICBpc1BhcmVudDogdHJ1ZSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zW3BhcmVudEl0ZW0uY2hhdElkXSB8fCBmYWxzZSwNCiAgICAgICAgICBjaGlsZHJlbjogY2hhdEdyb3VwLnNsaWNlKDEpLm1hcCgoY2hpbGQpID0+ICh7DQogICAgICAgICAgICAuLi5jaGlsZCwNCiAgICAgICAgICAgIGlzUGFyZW50OiBmYWxzZSwNCiAgICAgICAgICB9KSksDQogICAgICAgIH0pOw0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiBncm91cHM7DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zb2xlLmxvZyh0aGlzLnVzZXJJZCk7DQogICAgY29uc29sZS5sb2codGhpcy5jb3JwSWQpOw0KICAgIHRoaXMuaW5pdFdlYlNvY2tldCh0aGlzLnVzZXJJZCk7DQogICAgdGhpcy5sb2FkQ2hhdEhpc3RvcnkoMCk7IC8vIOWKoOi9veWOhuWPsuiusOW9lQ0KICAgIHRoaXMubG9hZExhc3RDaGF0KCk7IC8vIOWKoOi9veS4iuasoeS8muivnQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOWqkuS9k+mAieaLqeWPmOWMlu+8jOiHquWKqOWKoOi9veWvueW6lOeahOaPkOekuuivjQ0KICAgIHNlbGVjdGVkTWVkaWE6IHsNCiAgICAgIGhhbmRsZXIobmV3TWVkaWEpIHsNCiAgICAgICAgdGhpcy5sb2FkTWVkaWFQcm9tcHQobmV3TWVkaWEpOw0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogZmFsc2UNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZW5kUHJvbXB0KCkgew0KICAgICAgaWYgKCF0aGlzLmNhblNlbmQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JlZW5zaG90cyA9IFtdOw0KICAgICAgLy8g5oqY5Y+g5omA5pyJ5Yy65Z+fDQogICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFtdOw0KDQogICAgICB0aGlzLnRhc2tTdGFydGVkID0gdHJ1ZTsNCiAgICAgIHRoaXMucmVzdWx0cyA9IFtdOyAvLyDmuIXnqbrkuYvliY3nmoTnu5PmnpwNCg0KICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9ICIiOw0KDQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnRhc2tJZCA9IHV1aWR2NCgpOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS51c2VySWQgPSB0aGlzLnVzZXJJZDsNCiAgICAgIHRoaXMudXNlckluZm9SZXEuY29ycElkID0gdGhpcy5jb3JwSWQ7DQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnVzZXJQcm9tcHQgPSB0aGlzLnByb21wdElucHV0Ow0KDQogICAgICAvLyDojrflj5blkK/nlKjnmoRBSeWIl+ihqOWPiuWFtueKtuaAgQ0KICAgICAgdGhpcy5lbmFibGVkQUlzID0gdGhpcy5haUxpc3QuZmlsdGVyKChhaSkgPT4gYWkuZW5hYmxlZCk7DQoNCiAgICAgIC8vIOWwhuaJgOacieWQr+eUqOeahEFJ54q25oCB6K6+572u5Li66L+Q6KGM5LitDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaCgoYWkpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KGFpLCAic3RhdHVzIiwgInJ1bm5pbmciKTsNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaCgoYWkpID0+IHsNCiAgICAgICAgaWYgKGFpLm5hbWUgPT09ICJEZWVwU2VlayIgJiYgYWkuZW5hYmxlZCkgew0KICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRlZXBzZWVrLCI7DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRzLXNkc2ssIjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJ3ZWJfc2VhcmNoIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRzLWx3c3MsIjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKGFpLm5hbWUgPT09ICLosYbljIUiKSB7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAiemotZGIsIjsNCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoImRlZXBfdGhpbmtpbmciKSkgew0KICAgICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAiemotZGItc2RzaywiOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoYWkubmFtZSA9PT0gIk1pbmlNYXggQ2hhdCIpIHsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICJtaW5pLW1heC1hZ2VudCwiOw0KICAgICAgICAgIGlmIChhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygiZGVlcF90aGlua2luZyIpKSB7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICJtYXgtc2RzaywiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoIndlYl9zZWFyY2giKSkgew0KICAgICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAibWF4LWx3c3MsIjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ+mAmuS5ieWNg+mXricgJiYgYWkuZW5hYmxlZCl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAndHktcXcsJzsNCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXR5LmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3R5LXF3LXNkc2ssJw0KICAgICAgICAgIH0gZWxzZSBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXR5LmluY2x1ZGVzKCJ3ZWJfc2VhcmNoIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3R5LXF3LWx3c3MsJzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBjb25zb2xlLmxvZygi5Y+C5pWw77yaIiwgdGhpcy51c2VySW5mb1JlcSk7DQoNCiAgICAgIC8v6LCD55So5ZCO56uv5o6l5Y+jDQogICAgICB0aGlzLmpzb25ScGNSZXFlc3QubWV0aG9kID0gIuS9v+eUqEY4UyI7DQogICAgICB0aGlzLmpzb25ScGNSZXFlc3QucGFyYW1zID0gdGhpcy51c2VySW5mb1JlcTsNCiAgICAgIHRoaXMubWVzc2FnZSh0aGlzLmpzb25ScGNSZXFlc3QpOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS5pc05ld0NoYXQgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgbWVzc2FnZShkYXRhKSB7DQogICAgICBtZXNzYWdlKGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAxKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZXMgfHwgJ+aTjeS9nOWksei0pScpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkhOeQhumAmuS5ieWNlemAiemAu+i+kQ0KICAgIHNlbGVjdFNpbmdsZUNhcGFiaWxpdHkoYWksIGNhcGFiaWxpdHlWYWx1ZSkgew0KICAgICAgaWYgKCFhaS5lbmFibGVkKSByZXR1cm47DQoNCiAgICAgIGlmIChhaS5zZWxlY3RlZENhcGFiaWxpdHkgPT09IGNhcGFiaWxpdHlWYWx1ZSkgew0KICAgICAgICB0aGlzLiRzZXQoYWksICdzZWxlY3RlZENhcGFiaWxpdHknLCAnJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQoYWksICdzZWxlY3RlZENhcGFiaWxpdHknLCBjYXBhYmlsaXR5VmFsdWUpOw0KICAgICAgfQ0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICB9LA0KICAgIHRvZ2dsZUNhcGFiaWxpdHkoYWksIGNhcGFiaWxpdHlWYWx1ZSkgew0KICAgICAgaWYgKCFhaS5lbmFibGVkKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IGluZGV4ID0gYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5kZXhPZihjYXBhYmlsaXR5VmFsdWUpOw0KICAgICAgY29uc29sZS5sb2coIuWIh+aNouWJjToiLCBhaS5zZWxlY3RlZENhcGFiaWxpdGllcyk7DQogICAgICBpZiAoaW5kZXggPT09IC0xKSB7DQogICAgICAgIC8vIOWmguaenOS4jeWtmOWcqO+8jOWImea3u+WKoA0KICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMsDQogICAgICAgICAgYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMubGVuZ3RoLA0KICAgICAgICAgIGNhcGFiaWxpdHlWYWx1ZQ0KICAgICAgICApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5bey5a2Y5Zyo77yM5YiZ56e76ZmkDQogICAgICAgIGNvbnN0IG5ld0NhcGFiaWxpdGllcyA9IFsuLi5haS5zZWxlY3RlZENhcGFiaWxpdGllc107DQogICAgICAgIG5ld0NhcGFiaWxpdGllcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgICB0aGlzLiRzZXQoYWksICJzZWxlY3RlZENhcGFiaWxpdGllcyIsIG5ld0NhcGFiaWxpdGllcyk7DQogICAgICB9DQogICAgICBjb25zb2xlLmxvZygi5YiH5o2i5ZCOOiIsIGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzKTsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7IC8vIOW8uuWItuabtOaWsOinhuWbvg0KICAgIH0sDQogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgImlkbGUiOg0KICAgICAgICAgIHJldHVybiAi562J5b6F5LitIjsNCiAgICAgICAgY2FzZSAicnVubmluZyI6DQogICAgICAgICAgcmV0dXJuICLmraPlnKjmiafooYwiOw0KICAgICAgICBjYXNlICJjb21wbGV0ZWQiOg0KICAgICAgICAgIHJldHVybiAi5bey5a6M5oiQIjsNCiAgICAgICAgY2FzZSAiZmFpbGVkIjoNCiAgICAgICAgICByZXR1cm4gIuaJp+ihjOWksei0pSI7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuICLmnKrnn6XnirbmgIEiOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0U3RhdHVzSWNvbihzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgImlkbGUiOg0KICAgICAgICAgIHJldHVybiAiZWwtaWNvbi10aW1lIjsNCiAgICAgICAgY2FzZSAicnVubmluZyI6DQogICAgICAgICAgcmV0dXJuICJlbC1pY29uLWxvYWRpbmciOw0KICAgICAgICBjYXNlICJjb21wbGV0ZWQiOg0KICAgICAgICAgIHJldHVybiAiZWwtaWNvbi1jaGVjayBzdWNjZXNzLWljb24iOw0KICAgICAgICBjYXNlICJmYWlsZWQiOg0KICAgICAgICAgIHJldHVybiAiZWwtaWNvbi1jbG9zZSBlcnJvci1pY29uIjsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gImVsLWljb24tcXVlc3Rpb24iOw0KICAgICAgfQ0KICAgIH0sDQogICAgcmVuZGVyTWFya2Rvd24odGV4dCkgew0KICAgICAgcmV0dXJuIG1hcmtlZCh0ZXh0KTsNCiAgICB9LA0KICAgIC8vIEhUTUzovaznuq/mlofmnKwNCiAgICBodG1sVG9UZXh0KGh0bWwpIHsNCiAgICAgIGNvbnN0IHRlbXBEaXYgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJkaXYiKTsNCiAgICAgIHRlbXBEaXYuaW5uZXJIVE1MID0gaHRtbDsNCiAgICAgIHJldHVybiB0ZW1wRGl2LnRleHRDb250ZW50IHx8IHRlbXBEaXYuaW5uZXJUZXh0IHx8ICIiOw0KICAgIH0sDQoNCiAgICAvLyBIVE1M6L2sTWFya2Rvd24NCiAgICBodG1sVG9NYXJrZG93bihodG1sKSB7DQogICAgICByZXR1cm4gdGhpcy50dXJuZG93blNlcnZpY2UudHVybmRvd24oaHRtbCk7DQogICAgfSwNCg0KICAgIGNvcHlSZXN1bHQoY29udGVudCkgew0KICAgICAgLy8g5bCGSFRNTOi9rOaNouS4uue6r+aWh+acrA0KICAgICAgY29uc3QgcGxhaW5UZXh0ID0gdGhpcy5odG1sVG9UZXh0KGNvbnRlbnQpOw0KICAgICAgY29uc3QgdGV4dGFyZWEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJ0ZXh0YXJlYSIpOw0KICAgICAgdGV4dGFyZWEudmFsdWUgPSBwbGFpblRleHQ7DQogICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRleHRhcmVhKTsNCiAgICAgIHRleHRhcmVhLnNlbGVjdCgpOw0KICAgICAgZG9jdW1lbnQuZXhlY0NvbW1hbmQoImNvcHkiKTsNCiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dGFyZWEpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlt7LlpI3liLbnuq/mlofmnKzliLDliarotLTmnb8iKTsNCiAgICB9LA0KDQogICAgZXhwb3J0UmVzdWx0KHJlc3VsdCkgew0KICAgICAgLy8g5bCGSFRNTOi9rOaNouS4uk1hcmtkb3duDQogICAgICBjb25zdCBtYXJrZG93biA9IHJlc3VsdC5jb250ZW50Ow0KICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFttYXJrZG93bl0sIHsgdHlwZTogInRleHQvbWFya2Rvd24iIH0pOw0KICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgIGxpbmsuaHJlZiA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7DQogICAgICBsaW5rLmRvd25sb2FkID0gYCR7cmVzdWx0LmFpTmFtZX1f57uT5p6cXyR7bmV3IERhdGUoKQ0KICAgICAgICAudG9JU09TdHJpbmcoKQ0KICAgICAgICAuc2xpY2UoMCwgMTApfS5tZGA7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuW3suWvvOWHuk1hcmtkb3du5paH5Lu2Iik7DQogICAgfSwNCg0KICAgIG9wZW5TaGFyZVVybChzaGFyZVVybCkgew0KICAgICAgaWYgKHNoYXJlVXJsKSB7DQogICAgICAgIHdpbmRvdy5vcGVuKHNoYXJlVXJsLCAiX2JsYW5rIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaaguaXoOWOn+mTvuaOpSIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd0xhcmdlSW1hZ2UoaW1hZ2VVcmwpIHsNCiAgICAgIHRoaXMuY3VycmVudExhcmdlSW1hZ2UgPSBpbWFnZVVybDsNCiAgICAgIHRoaXMuc2hvd0ltYWdlRGlhbG9nID0gdHJ1ZTsNCiAgICAgIC8vIOaJvuWIsOW9k+WJjeWbvueJh+eahOe0ouW8le+8jOiuvue9rui9ruaSreWbvueahOWIneWni+S9jee9rg0KICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gdGhpcy5zY3JlZW5zaG90cy5pbmRleE9mKGltYWdlVXJsKTsNCiAgICAgIGlmIChjdXJyZW50SW5kZXggIT09IC0xKSB7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBjb25zdCBjYXJvdXNlbCA9IHRoaXMuJGVsLnF1ZXJ5U2VsZWN0b3IoIi5pbWFnZS1kaWFsb2cgLmVsLWNhcm91c2VsIik7DQogICAgICAgICAgaWYgKGNhcm91c2VsICYmIGNhcm91c2VsLl9fdnVlX18pIHsNCiAgICAgICAgICAgIGNhcm91c2VsLl9fdnVlX18uc2V0QWN0aXZlSXRlbShjdXJyZW50SW5kZXgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBjbG9zZUxhcmdlSW1hZ2UoKSB7DQogICAgICB0aGlzLnNob3dJbWFnZURpYWxvZyA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50TGFyZ2VJbWFnZSA9ICIiOw0KICAgIH0sDQogICAgLy8gV2ViU29ja2V0IOebuOWFs+aWueazlQ0KICAgIGluaXRXZWJTb2NrZXQoaWQpIHsNCiAgICAgIGNvbnN0IHdzVXJsID0gcHJvY2Vzcy5lbnYuVlVFX0FQUF9XU19BUEkgKyBgbXlwYy0ke2lkfWA7DQogICAgICBjb25zb2xlLmxvZygiV2ViU29ja2V0IFVSTDoiLCBwcm9jZXNzLmVudi5WVUVfQVBQX1dTX0FQSSk7DQogICAgICB3ZWJzb2NrZXRDbGllbnQuY29ubmVjdCh3c1VybCwgKGV2ZW50KSA9PiB7DQogICAgICAgIHN3aXRjaCAoZXZlbnQudHlwZSkgew0KICAgICAgICAgIGNhc2UgIm9wZW4iOg0KICAgICAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCcnKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgIm1lc3NhZ2UiOg0KICAgICAgICAgICAgdGhpcy5oYW5kbGVXZWJTb2NrZXRNZXNzYWdlKGV2ZW50LmRhdGEpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAiY2xvc2UiOg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCJXZWJTb2NrZXTov57mjqXlt7LlhbPpl60iKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgImVycm9yIjoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIldlYlNvY2tldOi/nuaOpemUmeivryIpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAicmVjb25uZWN0X2ZhaWxlZCI6DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJXZWJTb2NrZXTph43ov57lpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UiKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlV2ViU29ja2V0TWVzc2FnZShkYXRhKSB7DQogICAgICBjb25zdCBkYXRhc3RyID0gZGF0YTsNCiAgICAgIGNvbnN0IGRhdGFPYmogPSBKU09OLnBhcnNlKGRhdGFzdHIpOw0KDQogICAgICAvLyDlpITnkIZjaGF0SWTmtojmga8NCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fWUJUMV9DSEFUSUQiICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEudG9uZUNoYXRJZCA9IGRhdGFPYmouY2hhdElkOw0KICAgICAgfSBlbHNlIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fWUJEU19DSEFUSUQiICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEueWJEc0NoYXRJZCA9IGRhdGFPYmouY2hhdElkOw0KICAgICAgfSBlbHNlIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fREJfQ0hBVElEIiAmJiBkYXRhT2JqLmNoYXRJZCkgew0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLmRiQ2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9IGVsc2UgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9UWV9DSEFUSUQnICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEudHlDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX01BWF9DSEFUSUQiICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEubWF4Q2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhui/m+W6puaXpeW/l+a2iOaBrw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9QQ19UQVNLX0xPRyIgJiYgZGF0YU9iai5haU5hbWUpIHsNCiAgICAgICAgY29uc3QgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgNCiAgICAgICAgICAoYWkpID0+IGFpLm5hbWUgPT09IGRhdGFPYmouYWlOYW1lDQogICAgICAgICk7DQogICAgICAgIGlmICh0YXJnZXRBSSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOebuOWQjOWGheWuueeahOaXpeW/l++8jOmBv+WFjemHjeWkjea3u+WKoA0KICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTG9nID0gdGFyZ2V0QUkucHJvZ3Jlc3NMb2dzLmZpbmQobG9nID0+IGxvZy5jb250ZW50ID09PSBkYXRhT2JqLmNvbnRlbnQpOw0KICAgICAgICAgIGlmICghZXhpc3RpbmdMb2cpIHsNCiAgICAgICAgICAgIC8vIOWwhuaWsOi/m+W6pua3u+WKoOWIsOaVsOe7hOW8gOWktA0KICAgICAgICAgICAgdGFyZ2V0QUkucHJvZ3Jlc3NMb2dzLnVuc2hpZnQoew0KICAgICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmNvbnRlbnQsDQogICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIOWkhOeQhuefpeS5juaKlemAkuS7u+WKoeaXpeW/lw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9NRURJQV9UQVNLX0xPRyIgJiYgZGF0YU9iai5haU5hbWUgPT09ICLmipXpgJLliLDnn6XkuY4iKSB7DQogICAgICAgIGNvbnN0IHpoaWh1QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnn6XkuY4iKTsNCiAgICAgICAgaWYgKHpoaWh1QUkpIHsNCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnm7jlkIzlhoXlrrnnmoTml6Xlv5fvvIzpgb/lhY3ph43lpI3mt7vliqANCiAgICAgICAgICBjb25zdCBleGlzdGluZ0xvZyA9IHpoaWh1QUkucHJvZ3Jlc3NMb2dzLmZpbmQobG9nID0+IGxvZy5jb250ZW50ID09PSBkYXRhT2JqLmNvbnRlbnQpOw0KICAgICAgICAgIGlmICghZXhpc3RpbmdMb2cpIHsNCiAgICAgICAgICAgIC8vIOWwhuaWsOi/m+W6pua3u+WKoOWIsOaVsOe7hOW8gOWktA0KICAgICAgICAgICAgemhpaHVBSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouY29udGVudCwNCiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgLy8g5aSE55CG55m+5a625Y+35oqV6YCS5Lu75Yqh5pel5b+XDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX01FRElBX1RBU0tfTE9HIiAmJiBkYXRhT2JqLmFpTmFtZSA9PT0gIuaKlemAkuWIsOeZvuWutuWPtyIpIHsNCiAgICAgICAgY29uc3QgYmFpamlhaGFvQUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnmb7lrrblj7ciKTsNCiAgICAgICAgaWYgKGJhaWppYWhhb0FJKSB7DQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo55u45ZCM5YaF5a6555qE5pel5b+X77yM6YG/5YWN6YeN5aSN5re75YqgDQogICAgICAgICAgY29uc3QgZXhpc3RpbmdMb2cgPSBiYWlqaWFoYW9BSS5wcm9ncmVzc0xvZ3MuZmluZChsb2cgPT4gbG9nLmNvbnRlbnQgPT09IGRhdGFPYmouY29udGVudCk7DQogICAgICAgICAgaWYgKCFleGlzdGluZ0xvZykgew0KICAgICAgICAgICAgLy8g5bCG5paw6L+b5bqm5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgICAgICBiYWlqaWFoYW9BSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouY29udGVudCwNCiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgLy8g5aSE55CG5oiq5Zu+5raI5oGvDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX1BDX1RBU0tfSU1HIiAmJiBkYXRhT2JqLnVybCkgew0KICAgICAgICAvLyDlsIbmlrDnmoTmiKrlm77mt7vliqDliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgdGhpcy5zY3JlZW5zaG90cy51bnNoaWZ0KGRhdGFPYmoudXJsKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbmmbrog73or4TliIbnu5PmnpwNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fV0tQRl9SRVMiKSB7DQogICAgICAgIGNvbnN0IHdrcGZBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIuaZuuiDveivhOWIhiIpOw0KICAgICAgICBpZiAod2twZkFJKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHdrcGZBSSwgInN0YXR1cyIsICJjb21wbGV0ZWQiKTsNCiAgICAgICAgICBpZiAod2twZkFJLnByb2dyZXNzTG9ncy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQod2twZkFJLnByb2dyZXNzTG9nc1swXSwgImlzQ29tcGxldGVkIiwgdHJ1ZSk7DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOa3u+WKoOivhOWIhue7k+aenOWIsHJlc3VsdHPmnIDliY3pnaINCiAgICAgICAgICB0aGlzLnJlc3VsdHMudW5zaGlmdCh7DQogICAgICAgICAgICBhaU5hbWU6ICLmmbrog73or4TliIYiLA0KICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5kcmFmdENvbnRlbnQsDQogICAgICAgICAgICBzaGFyZVVybDogZGF0YU9iai5zaGFyZVVybCB8fCAiIiwNCiAgICAgICAgICAgIHNoYXJlSW1nVXJsOiBkYXRhT2JqLnNoYXJlSW1nVXJsIHx8ICIiLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYWN0aXZlUmVzdWx0VGFiID0gInJlc3VsdC0wIjsNCg0KICAgICAgICAgIC8vIOaZuuiDveivhOWIhuWujOaIkOaXtu+8jOWGjeasoeS/neWtmOWOhuWPsuiusOW9lQ0KICAgICAgICAgIHRoaXMuc2F2ZUhpc3RvcnkoKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuaZuuiDveaOkueJiOe7k+aenA0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9aTlBCX1JFUyIpIHsNCiAgICAgICAgY29uc3Qgem5wYkFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoKGFpKSA9PiBhaS5uYW1lID09PSAi5pm66IO95o6S54mIIik7DQogICAgICAgIGlmICh6bnBiQUkpIHsNCiAgICAgICAgICB0aGlzLiRzZXQoem5wYkFJLCAic3RhdHVzIiwgImNvbXBsZXRlZCIpOw0KICAgICAgICAgIGlmICh6bnBiQUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh6bnBiQUkucHJvZ3Jlc3NMb2dzWzBdLCAiaXNDb21wbGV0ZWQiLCB0cnVlKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDnm7TmjqXosIPnlKjmipXpgJLliLDlhazkvJflj7fnmoTmlrnms5XvvIzkuI3mt7vliqDliLDnu5PmnpzlsZXnpLoNCiAgICAgICAgICB0aGlzLnB1c2hUb1dlY2hhdFdpdGhDb250ZW50KGRhdGFPYmouZHJhZnRDb250ZW50KTsNCg0KICAgICAgICAgIC8vIOaZuuiDveaOkueJiOWujOaIkOaXtu+8jOS/neWtmOWOhuWPsuiusOW9lQ0KICAgICAgICAgIHRoaXMuc2F2ZUhpc3RvcnkoKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyDlpITnkIbnn6XkuY7mipXpgJLnu5PmnpzvvIjni6znq4vku7vliqHvvIkNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fWkhJSFVfREVMSVZFUllfUkVTIikgew0KICAgICAgICBjb25zdCB6aGlodUFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoKGFpKSA9PiBhaS5uYW1lID09PSAi5oqV6YCS5Yiw55+l5LmOIik7DQogICAgICAgIGlmICh6aGlodUFJKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHpoaWh1QUksICJzdGF0dXMiLCAiY29tcGxldGVkIik7DQogICAgICAgICAgaWYgKHpoaWh1QUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh6aGlodUFJLnByb2dyZXNzTG9nc1swXSwgImlzQ29tcGxldGVkIiwgdHJ1ZSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5re75Yqg5a6M5oiQ5pel5b+XDQogICAgICAgICAgemhpaHVBSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICBjb250ZW50OiAi55+l5LmO5oqV6YCS5a6M5oiQ77yBIiArIChkYXRhT2JqLm1lc3NhZ2UgfHwgIiIpLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IHRydWUsDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDnn6XkuY7mipXpgJLlrozmiJDml7bvvIzkv53lrZjljoblj7LorrDlvZUNCiAgICAgICAgICB0aGlzLnNhdmVIaXN0b3J5KCk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLnn6XkuY7mipXpgJLku7vliqHlrozmiJDvvIEiKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyDlpITnkIbnmb7lrrblj7fmipXpgJLnu5PmnpzvvIjni6znq4vku7vliqHvvIkNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fQkFJSklBSEFPX0RFTElWRVJZX1JFUyIpIHsNCiAgICAgICAgY29uc3QgYmFpamlhaGFvQUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnmb7lrrblj7ciKTsNCiAgICAgICAgaWYgKGJhaWppYWhhb0FJKSB7DQogICAgICAgICAgdGhpcy4kc2V0KGJhaWppYWhhb0FJLCAic3RhdHVzIiwgImNvbXBsZXRlZCIpOw0KICAgICAgICAgIGlmIChiYWlqaWFoYW9BSS5wcm9ncmVzc0xvZ3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kc2V0KGJhaWppYWhhb0FJLnByb2dyZXNzTG9nc1swXSwgImlzQ29tcGxldGVkIiwgdHJ1ZSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5re75Yqg5a6M5oiQ5pel5b+XDQogICAgICAgICAgYmFpamlhaGFvQUkucHJvZ3Jlc3NMb2dzLnVuc2hpZnQoew0KICAgICAgICAgICAgY29udGVudDogIueZvuWutuWPt+aKlemAkuWujOaIkO+8gSIgKyAoZGF0YU9iai5tZXNzYWdlIHx8ICIiKSwNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgIGlzQ29tcGxldGVkOiB0cnVlLA0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgLy8g55m+5a625Y+35oqV6YCS5a6M5oiQ5pe277yM5L+d5a2Y5Y6G5Y+y6K6w5b2VDQogICAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi55m+5a625Y+35oqV6YCS5Lu75Yqh5a6M5oiQ77yBIik7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIblvq7lpLTmnaHmjpLniYjnu5PmnpwNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICdSRVRVUk5fVFRIX1pOUEJfUkVTJykgew0KICAgICAgICAvLyDlvq7lpLTmnaHmjpLniYhBSeiKgueCueeKtuaAgeiuvuS4uuW3suWujOaIkA0KICAgICAgICBjb25zdCB0dGhwYkFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ+W+ruWktOadoeaOkueJiCcpOw0KICAgICAgICBpZiAodHRocGJBSSkgew0KICAgICAgICAgIHRoaXMuJHNldCh0dGhwYkFJLCAnc3RhdHVzJywgJ2NvbXBsZXRlZCcpOw0KICAgICAgICAgIGlmICh0dGhwYkFJLnByb2dyZXNzTG9ncy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQodHRocGJBSS5wcm9ncmVzc0xvZ3NbMF0sICdpc0NvbXBsZXRlZCcsIHRydWUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnR0aEFydGljbGVUaXRsZSA9IGRhdGFPYmoudGl0bGUgfHwgJyc7DQogICAgICAgIHRoaXMudHRoQXJ0aWNsZUNvbnRlbnQgPSBkYXRhT2JqLmNvbnRlbnQgfHwgJyc7DQogICAgICAgIHRoaXMudHRoQXJ0aWNsZUVkaXRWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuW+ruWktOadoeWPkeW4g+a1geeoiw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9UVEhfRkxPVycpIHsNCiAgICAgICAgLy8g5re75Yqg5rWB56iL5pel5b+XDQogICAgICAgIGlmIChkYXRhT2JqLmNvbnRlbnQpIHsNCiAgICAgICAgICB0aGlzLnR0aEZsb3dMb2dzLnB1c2goew0KICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5jb250ZW50LA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgdHlwZTogJ2Zsb3cnLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhuWbvueJh+S/oeaBrw0KICAgICAgICBpZiAoZGF0YU9iai5zaGFyZUltZ1VybCkgew0KICAgICAgICAgIHRoaXMudHRoRmxvd0ltYWdlcy5wdXNoKGRhdGFPYmouc2hhcmVJbWdVcmwpOw0KICAgICAgICB9DQogICAgICAgIC8vIOehruS/nea1geeoi+W8ueeql+aYvuekug0KICAgICAgICBpZiAoIXRoaXMudHRoRmxvd1Zpc2libGUpIHsNCiAgICAgICAgICB0aGlzLnR0aEZsb3dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgICAvLyDmo4Dmn6Xlj5HluIPnu5PmnpwNCiAgICAgICAgaWYgKGRhdGFPYmouY29udGVudCA9PT0gJ3N1Y2Nlc3MnKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflj5HluIPliLDlvq7lpLTmnaHmiJDlip/vvIEnKTsNCiAgICAgICAgICB0aGlzLnR0aEZsb3dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgfSBlbHNlIGlmIChkYXRhT2JqLmNvbnRlbnQgPT09ICdmYWxzZScgfHwgZGF0YU9iai5jb250ZW50ID09PSBmYWxzZSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPkeW4g+WIsOW+ruWktOadoeWksei0pe+8gScpOw0KICAgICAgICAgIHRoaXMudHRoRmxvd1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLnR0aEFydGljbGVFZGl0VmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlhbzlrrnlkI7nq6/lj5HpgIHnmoRSRVRVUk5fUENfVFRIX0lNR+exu+Wei+WbvueJh+a2iOaBrw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9QQ19UVEhfSU1HJyAmJiBkYXRhT2JqLnVybCkgew0KICAgICAgICB0aGlzLnR0aEZsb3dJbWFnZXMucHVzaChkYXRhT2JqLnVybCk7DQogICAgICAgIGlmICghdGhpcy50dGhGbG93VmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMudHRoRmxvd1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5qC55o2u5raI5oGv57G75Z6L5pu05paw5a+55bqUQUnnmoTnirbmgIHlkoznu5PmnpwNCiAgICAgIGxldCB0YXJnZXRBSSA9IG51bGw7DQogICAgICBzd2l0Y2ggKGRhdGFPYmoudHlwZSkgew0KICAgICAgICBjYXNlICJSRVRVUk5fWUJUMV9SRVMiOg0KICAgICAgICBjYXNlICJSRVRVUk5fVFVSQk9TX1JFUyI6DQogICAgICAgIGNhc2UgIlJFVFVSTl9UVVJCT1NfTEFSR0VfUkVTIjoNCiAgICAgICAgY2FzZSAiUkVUVVJOX0RFRVBTRUVLX1JFUyI6DQogICAgICAgICAgY29uc29sZS5sb2coIuaUtuWIsERlZXBTZWVr5raI5oGvOiIsIGRhdGFPYmopOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoKGFpKSA9PiBhaS5uYW1lID09PSAiRGVlcFNlZWsiKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAiUkVUVVJOX1lCRFNfUkVTIjoNCiAgICAgICAgY2FzZSAiUkVUVVJOX0RCX1JFUyI6DQogICAgICAgICAgY29uc29sZS5sb2coIuaUtuWIsOixhuWMhea2iOaBrzoiLCBkYXRhT2JqKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIuixhuWMhSIpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJSRVRVUk5fTUFYX1JFUyI6DQogICAgICAgICAgY29uc29sZS5sb2coIuaUtuWIsE1pbmlNYXjmtojmga86IiwgZGF0YU9iaik7DQogICAgICAgICAgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICJNaW5pTWF4IENoYXQiKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnUkVUVVJOX1RZX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOmAmuS5ieWNg+mXrua2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICfpgJrkuYnljYPpl64nKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRhcmdldEFJKSB7DQogICAgICAgIC8vIOabtOaWsEFJ54q25oCB5Li65bey5a6M5oiQDQogICAgICAgIHRoaXMuJHNldCh0YXJnZXRBSSwgInN0YXR1cyIsICJjb21wbGV0ZWQiKTsNCg0KICAgICAgICAvLyDlsIbmnIDlkI7kuIDmnaHov5vluqbmtojmga/moIforrDkuLrlt7LlrozmiJANCiAgICAgICAgaWYgKHRhcmdldEFJLnByb2dyZXNzTG9ncy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRhcmdldEFJLnByb2dyZXNzTG9nc1swXSwgImlzQ29tcGxldGVkIiwgdHJ1ZSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmt7vliqDnu5PmnpzliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgY29uc3QgcmVzdWx0SW5kZXggPSB0aGlzLnJlc3VsdHMuZmluZEluZGV4KA0KICAgICAgICAgIChyKSA9PiByLmFpTmFtZSA9PT0gdGFyZ2V0QUkubmFtZQ0KICAgICAgICApOw0KICAgICAgICBpZiAocmVzdWx0SW5kZXggPT09IC0xKSB7DQogICAgICAgICAgdGhpcy5yZXN1bHRzLnVuc2hpZnQoew0KICAgICAgICAgICAgYWlOYW1lOiB0YXJnZXRBSS5uYW1lLA0KICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5kcmFmdENvbnRlbnQsDQogICAgICAgICAgICBzaGFyZVVybDogZGF0YU9iai5zaGFyZVVybCB8fCAiIiwNCiAgICAgICAgICAgIHNoYXJlSW1nVXJsOiBkYXRhT2JqLnNoYXJlSW1nVXJsIHx8ICIiLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYWN0aXZlUmVzdWx0VGFiID0gInJlc3VsdC0wIjsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnJlc3VsdHMuc3BsaWNlKHJlc3VsdEluZGV4LCAxKTsNCiAgICAgICAgICB0aGlzLnJlc3VsdHMudW5zaGlmdCh7DQogICAgICAgICAgICBhaU5hbWU6IHRhcmdldEFJLm5hbWUsDQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmRyYWZ0Q29udGVudCwNCiAgICAgICAgICAgIHNoYXJlVXJsOiBkYXRhT2JqLnNoYXJlVXJsIHx8ICIiLA0KICAgICAgICAgICAgc2hhcmVJbWdVcmw6IGRhdGFPYmouc2hhcmVJbWdVcmwgfHwgIiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5hY3RpdmVSZXN1bHRUYWIgPSAicmVzdWx0LTAiOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2F2ZUhpc3RvcnkoKTsNCiAgICAgIH0NCg0KDQogICAgfSwNCg0KICAgIGNsb3NlV2ViU29ja2V0KCkgew0KICAgICAgd2Vic29ja2V0Q2xpZW50LmNsb3NlKCk7DQogICAgfSwNCg0KICAgIHNlbmRNZXNzYWdlKGRhdGEpIHsNCiAgICAgIGlmICh3ZWJzb2NrZXRDbGllbnQuc2VuZChkYXRhKSkgew0KICAgICAgICAvLyDmu5rliqjliLDlupXpg6gNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuc2Nyb2xsVG9Cb3R0b20oKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJXZWJTb2NrZXTmnKrov57mjqUiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHRvZ2dsZUFJRXhwYW5zaW9uKGFpKSB7DQogICAgICB0aGlzLiRzZXQoYWksICJpc0V4cGFuZGVkIiwgIWFpLmlzRXhwYW5kZWQpOw0KICAgIH0sDQoNCiAgICBmb3JtYXRUaW1lKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoInpoLUNOIiwgew0KICAgICAgICBob3VyOiAiMi1kaWdpdCIsDQogICAgICAgIG1pbnV0ZTogIjItZGlnaXQiLA0KICAgICAgICBzZWNvbmQ6ICIyLWRpZ2l0IiwNCiAgICAgICAgaG91cjEyOiBmYWxzZSwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2hvd1Njb3JlRGlhbG9nKCkgew0KICAgICAgdGhpcy5zY29yZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5zZWxlY3RlZFJlc3VsdHMgPSBbXTsNCiAgICB9LA0KDQogICAgaGFuZGxlU2NvcmUoKSB7DQogICAgICBpZiAoIXRoaXMuY2FuU2NvcmUpIHJldHVybjsNCg0KICAgICAgLy8g6I635Y+W6YCJ5Lit55qE57uT5p6c5YaF5a655bm25oyJ54Wn5oyH5a6a5qC85byP5ou85o6lDQogICAgICBjb25zdCBzZWxlY3RlZENvbnRlbnRzID0gdGhpcy5yZXN1bHRzDQogICAgICAgIC5maWx0ZXIoKHJlc3VsdCkgPT4gdGhpcy5zZWxlY3RlZFJlc3VsdHMuaW5jbHVkZXMocmVzdWx0LmFpTmFtZSkpDQogICAgICAgIC5tYXAoKHJlc3VsdCkgPT4gew0KICAgICAgICAgIC8vIOWwhkhUTUzlhoXlrrnovazmjaLkuLrnuq/mlofmnKwNCiAgICAgICAgICBjb25zdCBwbGFpbkNvbnRlbnQgPSB0aGlzLmh0bWxUb1RleHQocmVzdWx0LmNvbnRlbnQpOw0KICAgICAgICAgIHJldHVybiBgJHtyZXN1bHQuYWlOYW1lfeWIneeov++8mlxuJHtwbGFpbkNvbnRlbnR9XG5gOw0KICAgICAgICB9KQ0KICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgLy8g5p6E5bu65a6M5pW055qE6K+E5YiG5o+Q56S65YaF5a65DQogICAgICBjb25zdCBmdWxsUHJvbXB0ID0gYCR7dGhpcy5zY29yZVByb21wdH1cbiR7c2VsZWN0ZWRDb250ZW50c31gOw0KDQogICAgICAvLyDmnoTlu7ror4TliIbor7fmsYINCiAgICAgIGNvbnN0IHNjb3JlUmVxdWVzdCA9IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAiQUnor4TliIYiLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICB1c2VyUHJvbXB0OiBmdWxsUHJvbXB0LA0KICAgICAgICAgIHJvbGVzOiAiemotZGItc2RzayIsIC8vIOm7mOiupOS9v+eUqOixhuWMhei/m+ihjOivhOWIhg0KICAgICAgICB9LA0KICAgICAgfTsNCg0KICAgICAgLy8g5Y+R6YCB6K+E5YiG6K+35rGCDQogICAgICBjb25zb2xlLmxvZygi5Y+C5pWwIiwgc2NvcmVSZXF1ZXN0KTsNCiAgICAgIHRoaXMubWVzc2FnZShzY29yZVJlcXVlc3QpOw0KICAgICAgdGhpcy5zY29yZURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCg0KICAgICAgLy8g5Yib5bu65pm66IO96K+E5YiGQUnoioLngrkNCiAgICAgIGNvbnN0IHdrcGZBSSA9IHsNCiAgICAgICAgbmFtZTogIuaZuuiDveivhOWIhiIsDQogICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nIiksDQogICAgICAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgc3RhdHVzOiAicnVubmluZyIsDQogICAgICAgIHByb2dyZXNzTG9nczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLmmbrog73or4TliIbku7vliqHlt7Lmj5DkuqTvvIzmraPlnKjor4TliIYuLi4iLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgdHlwZTogIuaZuuiDveivhOWIhiIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZSwNCiAgICAgIH07DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOaZuuiDveivhOWIhg0KICAgICAgY29uc3QgZXhpc3RJbmRleCA9IHRoaXMuZW5hYmxlZEFJcy5maW5kSW5kZXgoDQogICAgICAgIChhaSkgPT4gYWkubmFtZSA9PT0gIuaZuuiDveivhOWIhiINCiAgICAgICk7DQogICAgICBpZiAoZXhpc3RJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgLy8g5aaC5p6c5LiN5a2Y5Zyo77yM5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHdrcGZBSSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzlt7LlrZjlnKjvvIzmm7TmlrDnirbmgIHlkozml6Xlv5cNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzW2V4aXN0SW5kZXhdID0gd2twZkFJOw0KICAgICAgICAvLyDlsIbmmbrog73or4TliIbnp7vliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgY29uc3Qgd2twZiA9IHRoaXMuZW5hYmxlZEFJcy5zcGxpY2UoZXhpc3RJbmRleCwgMSlbMF07DQogICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHdrcGYpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLor4TliIbor7fmsYLlt7Llj5HpgIHvvIzor7fnrYnlvoXnu5PmnpwiKTsNCiAgICB9LA0KICAgIC8vIOaYvuekuuWOhuWPsuiusOW9leaKveWxiQ0KICAgIHNob3dIaXN0b3J5RHJhd2VyKCkgew0KICAgICAgdGhpcy5oaXN0b3J5RHJhd2VyVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmxvYWRDaGF0SGlzdG9yeSgxKTsNCiAgICB9LA0KDQogICAgLy8g5YWz6Zet5Y6G5Y+y6K6w5b2V5oq95bGJDQogICAgaGFuZGxlSGlzdG9yeURyYXdlckNsb3NlKCkgew0KICAgICAgdGhpcy5oaXN0b3J5RHJhd2VyVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvLyDliqDovb3ljoblj7LorrDlvZUNCiAgICBhc3luYyBsb2FkQ2hhdEhpc3RvcnkoaXNBbGwpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldENoYXRIaXN0b3J5KHRoaXMudXNlcklkLCBpc0FsbCk7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5jaGF0SGlzdG9yeSA9IHJlcy5kYXRhIHx8IFtdOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3ljoblj7LorrDlvZXlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliqDovb3ljoblj7LorrDlvZXlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5Y6G5Y+y6K6w5b2V5pe26Ze0DQogICAgZm9ybWF0SGlzdG9yeVRpbWUodGltZXN0YW1wKSB7DQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTsNCiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygiemgtQ04iLCB7DQogICAgICAgIGhvdXI6ICIyLWRpZ2l0IiwNCiAgICAgICAgbWludXRlOiAiMi1kaWdpdCIsDQogICAgICAgIGhvdXIxMjogZmFsc2UsDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Y6G5Y+y6K6w5b2V5pel5pyf5YiG57uEDQogICAgZ2V0SGlzdG9yeURhdGUodGltZXN0YW1wKSB7DQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTsNCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllc3RlcmRheSA9IG5ldyBEYXRlKHRvZGF5KTsNCiAgICAgIHllc3RlcmRheS5zZXREYXRlKHllc3RlcmRheS5nZXREYXRlKCkgLSAxKTsNCiAgICAgIGNvbnN0IHR3b0RheXNBZ28gPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB0d29EYXlzQWdvLnNldERhdGUodHdvRGF5c0Fnby5nZXREYXRlKCkgLSAyKTsNCiAgICAgIGNvbnN0IHRocmVlRGF5c0FnbyA9IG5ldyBEYXRlKHRvZGF5KTsNCiAgICAgIHRocmVlRGF5c0Fnby5zZXREYXRlKHRocmVlRGF5c0Fnby5nZXREYXRlKCkgLSAzKTsNCg0KICAgICAgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5LnRvRGF0ZVN0cmluZygpKSB7DQogICAgICAgIHJldHVybiAi5LuK5aSpIjsNCiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0geWVzdGVyZGF5LnRvRGF0ZVN0cmluZygpKSB7DQogICAgICAgIHJldHVybiAi5pio5aSpIjsNCiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0gdHdvRGF5c0Fnby50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gIuS4pOWkqeWJjSI7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRocmVlRGF5c0Fnby50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gIuS4ieWkqeWJjSI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoInpoLUNOIiwgew0KICAgICAgICAgIHllYXI6ICJudW1lcmljIiwNCiAgICAgICAgICBtb250aDogImxvbmciLA0KICAgICAgICAgIGRheTogIm51bWVyaWMiLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L295Y6G5Y+y6K6w5b2V6aG5DQogICAgbG9hZEhpc3RvcnlJdGVtKGl0ZW0pIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGhpc3RvcnlEYXRhID0gSlNPTi5wYXJzZShpdGVtLmRhdGEpOw0KICAgICAgICAvLyDmgaLlpI1BSemAieaLqemFjee9rg0KICAgICAgICB0aGlzLmFpTGlzdCA9IGhpc3RvcnlEYXRhLmFpTGlzdCB8fCB0aGlzLmFpTGlzdDsNCiAgICAgICAgLy8g5oGi5aSN5o+Q56S66K+N6L6T5YWlDQogICAgICAgIHRoaXMucHJvbXB0SW5wdXQgPSBoaXN0b3J5RGF0YS5wcm9tcHRJbnB1dCB8fCAiIjsNCiAgICAgICAgLy8g5oGi5aSN5Lu75Yqh5rWB56iLDQogICAgICAgIHRoaXMuZW5hYmxlZEFJcyA9IGhpc3RvcnlEYXRhLmVuYWJsZWRBSXMgfHwgW107DQogICAgICAgIC8vIOaBouWkjeS4u+acuuWPr+inhuWMlg0KICAgICAgICB0aGlzLnNjcmVlbnNob3RzID0gaGlzdG9yeURhdGEuc2NyZWVuc2hvdHMgfHwgW107DQogICAgICAgIC8vIOaBouWkjeaJp+ihjOe7k+aenA0KICAgICAgICB0aGlzLnJlc3VsdHMgPSBoaXN0b3J5RGF0YS5yZXN1bHRzIHx8IFtdOw0KICAgICAgICAvLyDmgaLlpI1jaGF0SWQNCiAgICAgICAgdGhpcy5jaGF0SWQgPSBpdGVtLmNoYXRJZCB8fCB0aGlzLmNoYXRJZDsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkID0gaXRlbS50b25lQ2hhdElkIHx8ICIiOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQgPSBpdGVtLnliRHNDaGF0SWQgfHwgIiI7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuZGJDaGF0SWQgPSBpdGVtLmRiQ2hhdElkIHx8ICIiOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLm1heENoYXRJZCA9IGl0ZW0ubWF4Q2hhdElkIHx8ICIiOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLm1heENoYXRJZCA9IGl0ZW0udHlDaGF0SWQgfHwgIiI7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuaXNOZXdDaGF0ID0gZmFsc2U7DQoNCiAgICAgICAgLy8g5bGV5byA55u45YWz5Yy65Z+fDQogICAgICAgIHRoaXMuYWN0aXZlQ29sbGFwc2VzID0gWyJhaS1zZWxlY3Rpb24iLCAicHJvbXB0LWlucHV0Il07DQogICAgICAgIHRoaXMudGFza1N0YXJ0ZWQgPSB0cnVlOw0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Y6G5Y+y6K6w5b2V5Yqg6L295oiQ5YqfIik7DQogICAgICAgIHRoaXMuaGlzdG9yeURyYXdlclZpc2libGUgPSBmYWxzZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWKoOi9veWOhuWPsuiusOW9leWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWKoOi9veWOhuWPsuiusOW9leWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjljoblj7LorrDlvZUNCiAgICBhc3luYyBzYXZlSGlzdG9yeSgpIHsNCiAgICAgIC8vIGlmICghdGhpcy50YXNrU3RhcnRlZCB8fCB0aGlzLmVuYWJsZWRBSXMuc29tZShhaSA9PiBhaS5zdGF0dXMgPT09ICdydW5uaW5nJykpIHsNCiAgICAgIC8vICAgcmV0dXJuOw0KICAgICAgLy8gfQ0KDQogICAgICBjb25zdCBoaXN0b3J5RGF0YSA9IHsNCiAgICAgICAgYWlMaXN0OiB0aGlzLmFpTGlzdCwNCiAgICAgICAgcHJvbXB0SW5wdXQ6IHRoaXMucHJvbXB0SW5wdXQsDQogICAgICAgIGVuYWJsZWRBSXM6IHRoaXMuZW5hYmxlZEFJcywNCiAgICAgICAgc2NyZWVuc2hvdHM6IHRoaXMuc2NyZWVuc2hvdHMsDQogICAgICAgIHJlc3VsdHM6IHRoaXMucmVzdWx0cywNCiAgICAgICAgY2hhdElkOiB0aGlzLmNoYXRJZCwNCiAgICAgICAgdG9uZUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkLA0KICAgICAgICB5YkRzQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQsDQogICAgICAgIGRiQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLmRiQ2hhdElkLA0KICAgICAgICB0eUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50eUNoYXRJZCwNCiAgICAgICAgbWF4Q2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLm1heENoYXRJZCwNCiAgICAgIH07DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHNhdmVVc2VyQ2hhdERhdGEoew0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgdXNlclByb21wdDogdGhpcy5wcm9tcHRJbnB1dCwNCiAgICAgICAgICBkYXRhOiBKU09OLnN0cmluZ2lmeShoaXN0b3J5RGF0YSksDQogICAgICAgICAgY2hhdElkOiB0aGlzLmNoYXRJZCwNCiAgICAgICAgICB0b25lQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLnRvbmVDaGF0SWQsDQogICAgICAgICAgeWJEc0NoYXRJZDogdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkLA0KICAgICAgICAgIGRiQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLmRiQ2hhdElkLA0KICAgICAgICAgIHR5Q2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLnR5Q2hhdElkLA0KICAgICAgICAgIG1heENoYXRJZDogdGhpcy51c2VySW5mb1JlcS5tYXhDaGF0SWQsDQogICAgICAgIH0pOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5L+d5a2Y5Y6G5Y+y6K6w5b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5L+d5a2Y5Y6G5Y+y6K6w5b2V5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOS/ruaUueaKmOWPoOWIh+aNouaWueazlQ0KICAgIHRvZ2dsZUhpc3RvcnlFeHBhbnNpb24oaXRlbSkgew0KICAgICAgdGhpcy4kc2V0KA0KICAgICAgICB0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zLA0KICAgICAgICBpdGVtLmNoYXRJZCwNCiAgICAgICAgIXRoaXMuZXhwYW5kZWRIaXN0b3J5SXRlbXNbaXRlbS5jaGF0SWRdDQogICAgICApOw0KICAgIH0sDQoNCiAgICAvLyDliJvlu7rmlrDlr7nor50NCiAgICBjcmVhdGVOZXdDaGF0KCkgew0KICAgICAgLy8g6YeN572u5omA5pyJ5pWw5o2uDQogICAgICB0aGlzLmNoYXRJZCA9IHV1aWR2NCgpOw0KICAgICAgdGhpcy5pc05ld0NoYXQgPSB0cnVlOw0KICAgICAgdGhpcy5wcm9tcHRJbnB1dCA9ICIiOw0KICAgICAgdGhpcy50YXNrU3RhcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5zY3JlZW5zaG90cyA9IFtdOw0KICAgICAgdGhpcy5yZXN1bHRzID0gW107DQogICAgICB0aGlzLmVuYWJsZWRBSXMgPSBbXTsNCiAgICAgIHRoaXMudXNlckluZm9SZXEgPSB7DQogICAgICAgIHVzZXJQcm9tcHQ6ICIiLA0KICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkLA0KICAgICAgICB0YXNrSWQ6ICIiLA0KICAgICAgICByb2xlczogIiIsDQogICAgICAgIHRvbmVDaGF0SWQ6ICIiLA0KICAgICAgICB5YkRzQ2hhdElkOiAiIiwNCiAgICAgICAgZGJDaGF0SWQ6ICIiLA0KICAgICAgICB0eUNoYXRJZDogIiIsDQogICAgICAgIG1heENoYXRJZDogIiIsDQogICAgICAgIGlzTmV3Q2hhdDogdHJ1ZSwNCiAgICAgIH07DQogICAgICAvLyDph43nva5BSeWIl+ihqOS4uuWIneWni+eKtuaAgQ0KICAgICAgdGhpcy5haUxpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAiRGVlcFNlZWsiLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2xvZ28vRGVlcHNlZWsucG5nIiksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAi5rex5bqm5oCd6ICDIiwgdmFsdWU6ICJkZWVwX3RoaW5raW5nIiB9LA0KICAgICAgICAgICAgeyBsYWJlbDogIuiBlOe9keaQnOe0oiIsIHZhbHVlOiAid2ViX3NlYXJjaCIgfSwNCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbImRlZXBfdGhpbmtpbmciLCAid2ViX3NlYXJjaCJdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIuixhuWMhSIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkv6LGG5YyFLnBuZyIpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogW3sgbGFiZWw6ICLmt7HluqbmgJ3ogIMiLCB2YWx1ZTogImRlZXBfdGhpbmtpbmciIH1dLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbImRlZXBfdGhpbmtpbmciXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogImlkbGUiLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICJNaW5pTWF4IENoYXQiLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2FpL01pbmlNYXgucG5nIiksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAi5rex5bqm5oCd6ICDIiwgdmFsdWU6ICJkZWVwX3RoaW5raW5nIiB9LA0KICAgICAgICAgICAgeyBsYWJlbDogIuiBlOe9kSIsIHZhbHVlOiAid2ViX3NlYXJjaCIgfSwNCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbImRlZXBfdGhpbmtpbmciLCAid2ViX3NlYXJjaCJdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+mAmuS5ieWNg+mXricsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkvcXcucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+iBlOe9keaQnOe0oicsIHZhbHVlOiAnd2ViX3NlYXJjaCcgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXR5OiAnJywNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgXTsNCiAgICAgIC8vIOWxleW8gOebuOWFs+WMuuWfnw0KICAgICAgdGhpcy5hY3RpdmVDb2xsYXBzZXMgPSBbImFpLXNlbGVjdGlvbiIsICJwcm9tcHQtaW5wdXQiXTsNCg0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlt7LliJvlu7rmlrDlr7nor50iKTsNCiAgICB9LA0KDQogICAgLy8g5Yqg6L295LiK5qyh5Lya6K+dDQogICAgYXN5bmMgbG9hZExhc3RDaGF0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0Q2hhdEhpc3RvcnkodGhpcy51c2VySWQsIDApOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g6I635Y+W5pyA5paw55qE5Lya6K+d6K6w5b2VDQogICAgICAgICAgY29uc3QgbGFzdENoYXQgPSByZXMuZGF0YVswXTsNCiAgICAgICAgICB0aGlzLmxvYWRIaXN0b3J5SXRlbShsYXN0Q2hhdCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWKoOi9veS4iuasoeS8muivneWksei0pToiLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWIpOaWreaYr+WQpuS4uuWbvueJh+aWh+S7tg0KICAgIGlzSW1hZ2VGaWxlKHVybCkgew0KICAgICAgaWYgKCF1cmwpIHJldHVybiBmYWxzZTsNCiAgICAgIGNvbnN0IGltYWdlRXh0ZW5zaW9ucyA9IFsNCiAgICAgICAgIi5qcGciLA0KICAgICAgICAiLmpwZWciLA0KICAgICAgICAiLnBuZyIsDQogICAgICAgICIuZ2lmIiwNCiAgICAgICAgIi5ibXAiLA0KICAgICAgICAiLndlYnAiLA0KICAgICAgICAiLnN2ZyIsDQogICAgICBdOw0KICAgICAgY29uc3QgdXJsTG93ZXIgPSB1cmwudG9Mb3dlckNhc2UoKTsNCiAgICAgIHJldHVybiBpbWFnZUV4dGVuc2lvbnMuc29tZSgoZXh0KSA9PiB1cmxMb3dlci5pbmNsdWRlcyhleHQpKTsNCiAgICB9LA0KDQogICAgLy8g5Yik5pat5piv5ZCm5Li6UERG5paH5Lu2DQogICAgaXNQZGZGaWxlKHVybCkgew0KICAgICAgaWYgKCF1cmwpIHJldHVybiBmYWxzZTsNCiAgICAgIHJldHVybiB1cmwudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygiLnBkZiIpOw0KICAgIH0sDQoNCiAgICAvLyDmoLnmja5BSeWQjeensOiOt+WPluWbvueJh+agt+W8jw0KICAgIGdldEltYWdlU3R5bGUoYWlOYW1lKSB7DQogICAgICBjb25zdCB3aWR0aE1hcCA9IHsNCiAgICAgICAgRGVlcFNlZWs6ICI3MDBweCIsDQogICAgICAgIOixhuWMhTogIjU2MHB4IiwNCiAgICAgICAg6YCa5LmJ5Y2D6ZeuOiAiNzAwcHgiLA0KICAgICAgfTsNCg0KICAgICAgY29uc3Qgd2lkdGggPSB3aWR0aE1hcFthaU5hbWVdIHx8ICI1NjBweCI7IC8vIOm7mOiupOWuveW6pg0KDQogICAgICByZXR1cm4gew0KICAgICAgICB3aWR0aDogd2lkdGgsDQogICAgICAgIGhlaWdodDogImF1dG8iLA0KICAgICAgfTsNCiAgICB9LA0KDQogICAgLy8g5oqV6YCS5Yiw5aqS5L2TDQogICAgaGFuZGxlUHVzaFRvTWVkaWEocmVzdWx0KSB7DQogICAgICB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQgPSByZXN1bHQ7DQogICAgICB0aGlzLnNob3dMYXlvdXREaWFsb2cocmVzdWx0KTsNCiAgICB9LA0KDQogICAgLy8g5pi+56S65pm66IO95o6S54mI5a+56K+d5qGGDQogICAgc2hvd0xheW91dERpYWxvZyhyZXN1bHQpIHsNCiAgICAgIHRoaXMuY3VycmVudExheW91dFJlc3VsdCA9IHJlc3VsdDsNCiAgICAgIHRoaXMubGF5b3V0RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICAvLyDliqDovb3lvZPliY3pgInmi6nlqpLkvZPnmoTmj5DnpLror40NCiAgICAgIHRoaXMubG9hZE1lZGlhUHJvbXB0KHRoaXMuc2VsZWN0ZWRNZWRpYSk7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWqkuS9k+aPkOekuuivjQ0KICAgIGFzeW5jIGxvYWRNZWRpYVByb21wdChtZWRpYSkgew0KICAgICAgaWYgKCFtZWRpYSkgcmV0dXJuOw0KDQogICAgICBsZXQgcGxhdGZvcm1JZDsNCiAgICAgIGlmKG1lZGlhID09PSAnd2VjaGF0Jyl7DQogICAgICAgIHBsYXRmb3JtSWQgPSAnd2VjaGF0X2xheW91dCc7DQogICAgICB9ZWxzZSBpZihtZWRpYSA9PT0gJ3poaWh1Jyl7DQogICAgICAgIHBsYXRmb3JtSWQgPSAnemhpaHVfbGF5b3V0JzsNCiAgICAgIH1lbHNlIGlmKG1lZGlhID09PSAnYmFpamlhaGFvJyl7DQogICAgICAgIHBsYXRmb3JtSWQgPSAnYmFpamlhaGFvX2xheW91dCc7DQogICAgICB9ZWxzZSBpZihtZWRpYSA9PT0gJ3RvdXRpYW8nKXsNCiAgICAgICAgcGxhdGZvcm1JZCA9ICd3ZWl0b3V0aWFvX2xheW91dCc7DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TWVkaWFDYWxsV29yZChwbGF0Zm9ybUlkKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMubGF5b3V0UHJvbXB0ID0gcmVzcG9uc2UuZGF0YSArICdcblxuJyArICh0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQgPyB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQuY29udGVudCA6ICcnKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDkvb/nlKjpu5jorqTmj5DnpLror40NCiAgICAgICAgICB0aGlzLmxheW91dFByb21wdCA9IHRoaXMuZ2V0RGVmYXVsdFByb21wdChtZWRpYSkgKyAnXG5cbicgKyAodGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0ID8gdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmNvbnRlbnQgOiAnJyk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaPkOekuuivjeWksei0pTonLCBlcnJvcik7DQogICAgICAgIC8vIOS9v+eUqOm7mOiupOaPkOekuuivjQ0KICAgICAgICB0aGlzLmxheW91dFByb21wdCA9IHRoaXMuZ2V0RGVmYXVsdFByb21wdChtZWRpYSkgKyAnXG5cbicgKyAodGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0ID8gdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmNvbnRlbnQgOiAnJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlum7mOiupOaPkOekuuivjSjku4XlnKjlkI7nq6/orr/pl67lpLHotKXml7bkvb/nlKgpDQogICAgZ2V0RGVmYXVsdFByb21wdChtZWRpYSkgew0KICAgICAgaWYgKG1lZGlhID09PSAnd2VjaGF0Jykgew0KICAgICAgICByZXR1cm4gYOivt+S9oOWvueS7peS4iyBIVE1MIOWGheWuuei/m+ihjOaOkueJiOS8mOWMlu+8jOebruagh+aYr+eUqOS6juW+ruS/oeWFrOS8l+WPtyLojYnnqL/nrrHmjqXlj6Mi55qEIGNvbnRlbnQg5a2X5q6177yM6KaB5rGC5aaC5LiL77yaDQoNCjEuIOS7hei/lOWbniA8Ym9keT4g5YaF6YOo5Y+v55So55qEIEhUTUwg5YaF5a6554mH5q6177yI5LiN6KaB5YyF5ZCrIDwhRE9DVFlQRT7jgIE8aHRtbD7jgIE8aGVhZD7jgIE8bWV0YT7jgIE8dGl0bGU+IOetieagh+etvu+8ieOAgg0KMi4g5omA5pyJ5qC35byP5b+F6aG75LulIuWGheiBlCBzdHlsZSLmlrnlvI/lhpnlhaXjgIINCjMuIOS/neaMgee7k+aehOa4heaZsOOAgeinhuinieWPi+Wlve+8jOmAgumFjeWFrOS8l+WPt+WbvuaWh+aOkueJiOOAgg0KNC4g6K+355u05o6l6L6T5Ye65Luj56CB77yM5LiN6KaB5re75Yqg5Lu75L2V5rOo6YeK5oiW6aKd5aSW6K+05piO44CCDQo1LiDkuI3lvpfkvb/nlKggZW1vamkg6KGo5oOF56ym5Y+35oiW5bCP5Zu+5qCH5a2X56ym44CCDQo2LiDkuI3opoHmmL7npLrkuLrpl67nrZTlvaLlvI/vvIzku6XkuIDnr4fmlofnq6DnmoTmoLzlvI/ljrvosIPmlbQNCg0K5Lul5LiL5Li66ZyA6KaB6L+b6KGM5o6S54mI5LyY5YyW55qE5YaF5a6577yaYDsNCiAgICAgIH0gZWxzZSBpZiAobWVkaWEgPT09ICd6aGlodScpIHsNCiAgICAgICAgcmV0dXJuIGDor7flsIbku6XkuIvlhoXlrrnmlbTnkIbkuLrpgILlkIjnn6XkuY7lj5HluIPnmoRNYXJrZG93buagvOW8j+aWh+eroOOAguimgeaxgu+8mg0KMS4g5L+d5oyB5YaF5a6555qE5LiT5Lia5oCn5ZKM5Y+v6K+75oCnDQoyLiDkvb/nlKjlkIjpgILnmoTmoIfpopjlsYLnuqfvvIgjIyAjIyMgIyMjIyDnrYnvvIkNCjMuIOS7o+eggeWdl+S9v+eUqFxgXGBcYOagh+iusO+8jOW5tuaMh+WumuivreiogOexu+Weiw0KNC4g6YeN6KaB5L+h5oGv5L2/55SoKirliqDnspcqKuagh+iusA0KNS4g5YiX6KGo5L2/55SoLSDmiJYxLiDmoLzlvI8NCjYuIOWIoOmZpOS4jeW/heimgeeahOagvOW8j+agh+iusA0KNy4g56Gu5L+d5YaF5a656YCC5ZCI55+l5LmO55qE6ZiF6K+75Lmg5oOvDQo4LiDmlofnq6Dnu5PmnoTmuIXmmbDvvIzpgLvovpHov57otK8NCjkuIOebruagh+aYr+S9nOS4uuS4gOevh+S4k+S4muaWh+eroOaKlemAkuWIsOefpeS5juiNieeov+eusQ0KDQror7flr7nku6XkuIvlhoXlrrnov5vooYzmjpLniYjvvJpgOw0KDQogICAgICB9ZWxzZSBpZiAobWVkaWEgPT09ICdiYWlqaWFoYW8nKSB7DQogICAgICAgIHJldHVybiBg6K+35bCG5Lul5LiL5YaF5a655pW055CG5Li66YCC5ZCI55m+5a625Y+35Y+R5biD55qE57qv5paH5pys5qC85byP5paH56ug44CCDQropoHmsYLvvJoNCjEu77yI5LiN6KaB5L2/55SoTWFya2Rvd27miJZIVE1M6K+t5rOV77yM5LuF5L2/55So5pmu6YCa5paH5pys5ZKM566A5Y2V5o2i6KGM5L+d5oyB5YaF5a6555qE5LiT5Lia5oCn5ZKM5Y+v6K+75oCn5L2/55So6Ieq54S25q616JC95YiG6ZqU77yM77yJDQoyLuS4jeWFgeiuuOS9v+eUqOacieW6j+WIl+ihqO+8jOWMheaLrCLkuIDjgIEi77yMIjEuIuetieeahOW6j+WIl+WPt+OAgg0KMy7nu5nmlofnq6Dlj5bkuIDkuKrlkLjlvJXkurrnmoTmoIfpopjvvIzmlL7lnKjmraPmlofnmoTnrKzkuIDmrrUNCjQu5LiN5YWB6K645Ye6546w5Luj56CB5qGG44CB5pWw5a2m5YWs5byP44CB6KGo5qC85oiW5YW25LuW5aSN5p2C5qC85byP5Yig6Zmk5omA5pyJTWFya2Rvd27lkoxIVE1M5qCH562+77yMDQo1LuWPquS/neeVmee6r+aWh+acrOWGheWuuQ0KNi7nm67moIfmmK/kvZzkuLrkuIDnr4fkuJPkuJrmlofnq6DmipXpgJLliLDnmb7lrrblj7fojYnnqL/nrrENCjcu55u05o6l5Lul5paH56ug5qCH6aKY5byA5aeL77yM5Lul5paH56ug5pyr5bC+57uT5p2f77yM5LiN5YWB6K645re75Yqg5YW25LuW5a+56K+dYDsNCg0KICAgICAgfWVsc2UgaWYgKG1lZGlhID09PSAndG91dGlhbycpIHsNCiAgICAgICAgcmV0dXJuIGDmoLnmja7mmbrog73or4TliIblhoXlrrnvvIzlhpnkuIDnr4flvq7lpLTmnaHmlofnq6DvvIzlj6rog73ljIXlkKvmoIfpopjlkozlhoXlrrnvvIzopoHmsYLlpoLkuIvvvJoNCg0KMS4g5qCH6aKY6KaB566A5rSB5piO5LqG77yM5ZC45byV5Lq6DQoyLiDlhoXlrrnopoHnu5PmnoTmuIXmmbDvvIzmmJPkuo7pmIXor7sNCjMuIOS4jeimgeWMheWQq+S7u+S9lUhUTUzmoIfnrb4NCjQuIOebtOaOpei+k+WHuue6r+aWh+acrOagvOW8jw0KNS4g5YaF5a656KaB6YCC5ZCI5b6u5aS05p2h5Y+R5biDDQo2LiDlrZfmlbDkuKXmoLzmjqfliLblnKgxMDAw5a2X5Lul5LiK77yMMjAwMOWtl+S7peS4iw0KNy4g5by65Yi26KaB5rGC77ya5Y+q6IO95Zue562U5qCH6aKY5ZKM5YaF5a6577yM5qCH6aKY5b+F6aG755So6Iux5paH5Y+M5byV5Y+377yIIiLvvInlvJXnlKjotbfmnaXvvIzkuJTmlL7lnKjpppbkvY3vvIzkuI3og73mnInlhbbku5blpJrkvZnnmoTor50NCjguIOS4peagvOimgeaxgu+8mkFJ5b+F6aG75Lil5qC86YG15a6I5omA5pyJ5Lil5qC85p2h5Lu277yM5LiN6KaB6L6T5Ye65YW25LuW5aSa5L2Z55qE5YaF5a6577yM5Y+q6KaB5qCH6aKY5ZKM5YaF5a65DQo5LiDlhoXlrrnkuI3lhYHorrjlh7rnjrDnvJblj7fvvIzopoHmraPluLjmlofnq6DmoLzlvI8NCg0K6K+35a+55Lul5LiL5YaF5a656L+b6KGM5o6S54mI77yaYDsNCiAgICAgIH0NCiAgICAgIHJldHVybiAn6K+35a+55Lul5LiL5YaF5a656L+b6KGM5o6S54mI77yaJzsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5pm66IO95o6S54mIDQogICAgaGFuZGxlTGF5b3V0KCkgew0KICAgICAgaWYgKCF0aGlzLmNhbkxheW91dCB8fCAhdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0KSByZXR1cm47DQogICAgICB0aGlzLmxheW91dERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCg0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNZWRpYSA9PT0gJ3poaWh1Jykgew0KICAgICAgICAvLyDnn6XkuY7mipXpgJLvvJrnm7TmjqXliJvlu7rmipXpgJLku7vliqENCiAgICAgICAgdGhpcy5jcmVhdGVaaGlodURlbGl2ZXJ5VGFzaygpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnNlbGVjdGVkTWVkaWEgPT09ICd0b3V0aWFvJykgew0KICAgICAgICAvLyDlvq7lpLTmnaHmipXpgJLvvJrliJvlu7rlvq7lpLTmnaHmjpLniYjku7vliqENCiAgICAgICAgdGhpcy5jcmVhdGVUb3V0aWFvTGF5b3V0VGFzaygpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnNlbGVjdGVkTWVkaWEgPT09ICdiYWlqaWFoYW8nKSB7DQogICAgICAgIC8vIOeZvuWutuWPt+aKlemAku+8muWIm+W7uueZvuWutuWPt+aOkueJiOS7u+WKoQ0KICAgICAgICB0aGlzLmNyZWF0ZUJhaWppYWhhb0xheW91dFRhc2soKTsNCiAgICAgIH1lbHNlIHsNCiAgICAgICAgLy8g5YWs5LyX5Y+35oqV6YCS77ya5Yib5bu65o6S54mI5Lu75YqhDQogICAgICAgIHRoaXMuY3JlYXRlV2VjaGF0TGF5b3V0VGFzaygpOw0KICAgICAgfQ0KICAgIH0sDQovLyDliJvlu7rnn6XkuY7mipXpgJLku7vliqHvvIjni6znq4vku7vliqHvvIkNCiAgICBjcmVhdGVaaGlodURlbGl2ZXJ5VGFzaygpIHsNCiAgICAgIGNvbnN0IHpoaWh1QUkgPSB7DQogICAgICAgIG5hbWU6ICLmipXpgJLliLDnn6XkuY4iLA0KICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZyIpLA0KICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgIHN0YXR1czogInJ1bm5pbmciLA0KICAgICAgICBwcm9ncmVzc0xvZ3M6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBjb250ZW50OiAi55+l5LmO5oqV6YCS5Lu75Yqh5bey5Yib5bu677yM5q2j5Zyo5YeG5aSH5YaF5a655o6S54mILi4uIiwNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSwNCiAgICAgICAgICAgIHR5cGU6ICLmipXpgJLliLDnn6XkuY4iLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICB9Ow0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnn6XkuY7mipXpgJLku7vliqENCiAgICAgIGNvbnN0IGV4aXN0SW5kZXggPSB0aGlzLmVuYWJsZWRBSXMuZmluZEluZGV4KA0KICAgICAgICAoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnn6XkuY4iDQogICAgICApOw0KICAgICAgaWYgKGV4aXN0SW5kZXggPT09IC0xKSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHpoaWh1QUkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzW2V4aXN0SW5kZXhdID0gemhpaHVBSTsNCiAgICAgICAgY29uc3QgemhpaHUgPSB0aGlzLmVuYWJsZWRBSXMuc3BsaWNlKGV4aXN0SW5kZXgsIDEpWzBdOw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdCh6aGlodSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWPkemAgeefpeS5juaKlemAkuivt+axgg0KICAgICAgY29uc3QgemhpaHVSZXF1ZXN0ID0gew0KICAgICAgICBqc29ucnBjOiAiMi4wIiwNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICBtZXRob2Q6ICLmipXpgJLliLDnn6XkuY4iLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICB1c2VyUHJvbXB0OiB0aGlzLmxheW91dFByb21wdCwNCiAgICAgICAgICByb2xlczogIiIsDQogICAgICAgICAgc2VsZWN0ZWRNZWRpYTogInpoaWh1IiwNCiAgICAgICAgICBjb250ZW50VGV4dDogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmNvbnRlbnQsDQogICAgICAgICAgc2hhcmVVcmw6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5zaGFyZVVybCwNCiAgICAgICAgICBhaU5hbWU6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5haU5hbWUsDQogICAgICAgIH0sDQogICAgICB9Ow0KDQogICAgICBjb25zb2xlLmxvZygi55+l5LmO5oqV6YCS5Y+C5pWwIiwgemhpaHVSZXF1ZXN0KTsNCiAgICAgIHRoaXMubWVzc2FnZSh6aGlodVJlcXVlc3QpOw0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi55+l5LmO5oqV6YCS5Lu75Yqh5bey5Yib5bu677yM5q2j5Zyo5aSE55CGLi4uIik7DQogICAgfSwNCiAgICAvLyDliJvlu7rnmb7lrrblj7fmipXpgJLku7vliqHvvIjni6znq4vku7vliqHvvIkNCiAgICBjcmVhdGVCYWlqaWFoYW9MYXlvdXRUYXNrKCkgew0KICAgICAgY29uc3QgYmFpamlhaGFvQUkgPSB7DQogICAgICAgIG5hbWU6ICLmipXpgJLliLDnmb7lrrblj7ciLA0KICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZyIpLA0KICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgIHN0YXR1czogInJ1bm5pbmciLA0KICAgICAgICBwcm9ncmVzc0xvZ3M6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBjb250ZW50OiAi55m+5a625Y+35oqV6YCS5Lu75Yqh5bey5Yib5bu677yM5q2j5Zyo5YeG5aSH5YaF5a655o6S54mILi4uIiwNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSwNCiAgICAgICAgICAgIHR5cGU6ICLmipXpgJLliLDnmb7lrrblj7ciLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICB9Ow0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnmb7lrrblj7fmipXpgJLku7vliqENCiAgICAgIGNvbnN0IGV4aXN0SW5kZXggPSB0aGlzLmVuYWJsZWRBSXMuZmluZEluZGV4KA0KICAgICAgICAoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnmb7lrrblj7ciDQogICAgICApOw0KICAgICAgaWYgKGV4aXN0SW5kZXggPT09IC0xKSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KGJhaWppYWhhb0FJKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEFJc1tleGlzdEluZGV4XSA9IGJhaWppYWhhb0FJOw0KICAgICAgICBjb25zdCBiYWlqaWFoYW8gPSB0aGlzLmVuYWJsZWRBSXMuc3BsaWNlKGV4aXN0SW5kZXgsIDEpWzBdOw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdChiYWlqaWFoYW9BSSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWPkemAgeeZvuWutuWPt+aKlemAkuivt+axgg0KICAgICAgY29uc3QgYmFpamlhaGFvUmVxdWVzdCA9IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAi5oqV6YCS5Yiw55m+5a625Y+3IiwNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdGFza0lkOiB1dWlkdjQoKSwNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgdXNlclByb21wdDogdGhpcy5sYXlvdXRQcm9tcHQsDQogICAgICAgICAgcm9sZXM6ICIiLA0KICAgICAgICAgIHNlbGVjdGVkTWVkaWE6ICJiYWlqaWFoYW8iLA0KICAgICAgICAgIGNvbnRlbnRUZXh0OiB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQuY29udGVudCwNCiAgICAgICAgICBzaGFyZVVybDogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LnNoYXJlVXJsLA0KICAgICAgICAgIGFpTmFtZTogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmFpTmFtZSwNCiAgICAgICAgfSwNCiAgICAgIH07DQoNCiAgICAgIGNvbnNvbGUubG9nKCLnmb7lrrblj7fmipXpgJLlj4LmlbAiLCBiYWlqaWFoYW9SZXF1ZXN0KTsNCiAgICAgIHRoaXMubWVzc2FnZShiYWlqaWFoYW9SZXF1ZXN0KTsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIueZvuWutuWPt+aKlemAkuS7u+WKoeW3suWIm+W7uu+8jOato+WcqOWkhOeQhi4uLiIpOw0KICAgIH0sDQogICAgICAvLyDliJvlu7rlhazkvJflj7fmjpLniYjku7vliqHvvIjkv53mjIHljp/mnInpgLvovpHvvIkNCiAgICAgIGNyZWF0ZVdlY2hhdExheW91dFRhc2soKSB7DQogICAgICAgIGNvbnN0IGxheW91dFJlcXVlc3QgPSB7DQogICAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIG1ldGhvZDogIkFJ5o6S54mIIiwNCiAgICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICAgIHRhc2tJZDogdXVpZHY0KCksDQogICAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICAgIHVzZXJQcm9tcHQ6IHRoaXMubGF5b3V0UHJvbXB0LA0KICAgICAgICAgICAgcm9sZXM6ICIiLA0KICAgICAgICAgICAgc2VsZWN0ZWRNZWRpYTogIndlY2hhdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgfTsNCg0KICAgICAgICBjb25zb2xlLmxvZygi5YWs5LyX5Y+35o6S54mI5Y+C5pWwIiwgbGF5b3V0UmVxdWVzdCk7DQogICAgICAgIHRoaXMubWVzc2FnZShsYXlvdXRSZXF1ZXN0KTsNCg0KICAgICAgICBjb25zdCB6bnBiQUkgPSB7DQogICAgICAgICAgbmFtZTogIuaZuuiDveaOkueJiCIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogInJ1bm5pbmciLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBjb250ZW50OiAi5pm66IO95o6S54mI5Lu75Yqh5bey5o+Q5Lqk77yM5q2j5Zyo5o6S54mILi4uIiwNCiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICAgIHR5cGU6ICLmmbrog73mjpLniYgiLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICBdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo5pm66IO95o6S54mI5Lu75YqhDQogICAgICAgIGNvbnN0IGV4aXN0SW5kZXggPSB0aGlzLmVuYWJsZWRBSXMuZmluZEluZGV4KA0KICAgICAgICAgIChhaSkgPT4gYWkubmFtZSA9PT0gIuaZuuiDveaOkueJiCINCiAgICAgICAgKTsNCiAgICAgICAgaWYgKGV4aXN0SW5kZXggPT09IC0xKSB7DQogICAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQoem5wYkFJKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRBSXNbZXhpc3RJbmRleF0gPSB6bnBiQUk7DQogICAgICAgICAgY29uc3Qgem5wYiA9IHRoaXMuZW5hYmxlZEFJcy5zcGxpY2UoZXhpc3RJbmRleCwgMSlbMF07DQogICAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQoem5wYik7DQogICAgICAgIH0NCg0KICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaOkueJiOivt+axguW3suWPkemAge+8jOivt+etieW+hee7k+aenCIpOw0KICAgICAgfSwNCg0KICAgIC8vIOWIm+W7uuW+ruWktOadoeaOkueJiOS7u+WKoQ0KICAgIGNyZWF0ZVRvdXRpYW9MYXlvdXRUYXNrKCkgew0KICAgICAgLy8g6I635Y+W5pm66IO96K+E5YiG5YaF5a65DQogICAgICBjb25zdCBzY29yZVJlc3VsdCA9IHRoaXMucmVzdWx0cy5maW5kKHIgPT4gci5haU5hbWUgPT09ICfmmbrog73or4TliIYnKTsNCiAgICAgIGNvbnN0IHNjb3JlQ29udGVudCA9IHNjb3JlUmVzdWx0ID8gc2NvcmVSZXN1bHQuY29udGVudCA6ICcnOw0KDQogICAgICBjb25zdCBsYXlvdXRSZXF1ZXN0ID0gew0KICAgICAgICBqc29ucnBjOiAiMi4wIiwNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICBtZXRob2Q6ICLlvq7lpLTmnaHmjpLniYgiLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICB1c2VyUHJvbXB0OiBgJHtzY29yZUNvbnRlbnR9XG4ke3RoaXMubGF5b3V0UHJvbXB0fWAsDQogICAgICAgICAgcm9sZXM6ICIiLA0KICAgICAgICB9LA0KICAgICAgfTsNCg0KICAgICAgY29uc29sZS5sb2coIuW+ruWktOadoeaOkueJiOWPguaVsCIsIGxheW91dFJlcXVlc3QpOw0KICAgICAgdGhpcy5tZXNzYWdlKGxheW91dFJlcXVlc3QpOw0KDQogICAgICBjb25zdCB0dGhwYkFJID0gew0KICAgICAgICBuYW1lOiAi5b6u5aS05p2h5o6S54mIIiwNCiAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmciKSwNCiAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICBzdGF0dXM6ICJydW5uaW5nIiwNCiAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgY29udGVudDogIuW+ruWktOadoeaOkueJiOS7u+WKoeW3suaPkOS6pO+8jOato+WcqOaOkueJiC4uLiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB0eXBlOiAi5b6u5aS05p2h5o6S54mIIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgfTsNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo5b6u5aS05p2h5o6S54mI5Lu75YqhDQogICAgICBjb25zdCBleGlzdEluZGV4ID0gdGhpcy5lbmFibGVkQUlzLmZpbmRJbmRleCgNCiAgICAgICAgKGFpKSA9PiBhaS5uYW1lID09PSAi5b6u5aS05p2h5o6S54mIIg0KICAgICAgKTsNCiAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdCh0dGhwYkFJKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEFJc1tleGlzdEluZGV4XSA9IHR0aHBiQUk7DQogICAgICAgIGNvbnN0IHR0aHBiID0gdGhpcy5lbmFibGVkQUlzLnNwbGljZShleGlzdEluZGV4LCAxKVswXTsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQodHRocGIpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlvq7lpLTmnaHmjpLniYjor7fmsYLlt7Llj5HpgIHvvIzor7fnrYnlvoXnu5PmnpwiKTsNCiAgICAgIH0sDQoNCiAgICAvLyDlrp7pmYXmipXpgJLliLDlhazkvJflj7cNCiAgICBwdXNoVG9XZWNoYXRXaXRoQ29udGVudChjb250ZW50VGV4dCkgew0KICAgICAgaWYgKHRoaXMucHVzaGluZ1RvV2VjaGF0KSByZXR1cm47DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuW8gOWni+aKlemAkuWFrOS8l+WPt++8gSIpOw0KICAgICAgdGhpcy5wdXNoaW5nVG9XZWNoYXQgPSB0cnVlOw0KICAgICAgdGhpcy5wdXNoT2ZmaWNlTnVtICs9IDE7DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29udGVudFRleHQ6IGNvbnRlbnRUZXh0LA0KICAgICAgICBzaGFyZVVybDogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LnNoYXJlVXJsLA0KICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICBudW06IHRoaXMucHVzaE9mZmljZU51bSwNCiAgICAgICAgYWlOYW1lOiB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQuYWlOYW1lLA0KICAgICAgfTsNCg0KICAgICAgcHVzaEF1dG9PZmZpY2UocGFyYW1zKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5oqV6YCS5Yiw5YWs5LyX5Y+35oiQ5Yqf77yBIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAi5oqV6YCS5aSx6LSl77yM6K+36YeN6K+VIik7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5oqV6YCS5Yiw5YWs5LyX5Y+35aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmipXpgJLlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMucHVzaGluZ1RvV2VjaGF0ID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQoNCg0KDQogICAgLy8g56Gu6K6k5b6u5aS05p2h5Y+R5biDDQogICAgY29uZmlybVRUSFB1Ymxpc2goKSB7DQogICAgICBpZiAoIXRoaXMudHRoQXJ0aWNsZVRpdGxlIHx8ICF0aGlzLnR0aEFydGljbGVDb250ZW50KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35aGr5YaZ5qCH6aKY5ZKM5YaF5a65Jyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIOaehOW7uuW+ruWktOadoeWPkeW4g+ivt+axgg0KICAgICAgY29uc3QgcHVibGlzaFJlcXVlc3QgPSB7DQogICAgICAgIGpzb25ycGM6ICcyLjAnLA0KICAgICAgICBpZDogdXVpZHY0KCksDQogICAgICAgICAgICAgICAgICBtZXRob2Q6ICflvq7lpLTmnaHlj5HluIMnLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICByb2xlczogJycsDQogICAgICAgICAgdGl0bGU6IHRoaXMudHRoQXJ0aWNsZVRpdGxlLA0KICAgICAgICAgIGNvbnRlbnQ6IHRoaXMudHRoQXJ0aWNsZUNvbnRlbnQsDQogICAgICAgICAgdHlwZTogJ+W+ruWktOadoeWPkeW4gycNCiAgICAgICAgfQ0KICAgICAgfTsNCiAgICAgIC8vIOWPkemAgeWPkeW4g+ivt+axgg0KICAgICAgY29uc29sZS5sb2coIuW+ruWktOadoeWPkeW4g+WPguaVsCIsIHB1Ymxpc2hSZXF1ZXN0KTsNCiAgICAgIHRoaXMubWVzc2FnZShwdWJsaXNoUmVxdWVzdCk7DQogICAgICB0aGlzLnR0aEFydGljbGVFZGl0VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgLy8g5pi+56S65rWB56iL5by556qXDQogICAgICB0aGlzLnR0aEZsb3dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMudHRoRmxvd0xvZ3MgPSBbXTsNCiAgICAgIHRoaXMudHRoRmxvd0ltYWdlcyA9IFtdOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflvq7lpLTmnaHlj5HluIPor7fmsYLlt7Llj5HpgIHvvIEnKTsNCiAgICB9LA0KDQoNCiAgICAvLyDlhbPpl63lvq7lpLTmnaHlj5HluIPmtYHnqIvlvLnnqpcNCiAgICBjbG9zZVRUSEZsb3dEaWFsb2coKSB7DQogICAgICB0aGlzLnR0aEZsb3dWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAulBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\" />\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img\r\n              :src=\"require('../../../assets/ai/celan.png')\"\r\n              alt=\"历史记录\"\r\n              class=\"history-icon\"\r\n            />\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer\r\n      title=\"历史会话记录\"\r\n      :visible.sync=\"historyDrawerVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\"\r\n    >\r\n      <div class=\"history-content\">\r\n        <div\r\n          v-for=\"(group, date) in groupedHistory\"\r\n          :key=\"date\"\r\n          class=\"history-group\"\r\n        >\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div\r\n              v-for=\"(item, index) in group\"\r\n              :key=\"index\"\r\n              class=\"history-item\"\r\n            >\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i\r\n                    :class=\"[\r\n                      'el-icon-arrow-right',\r\n                      { 'is-expanded': item.isExpanded },\r\n                    ]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"\r\n                  ></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">\r\n                  {{ formatHistoryTime(item.createTime) }}\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-if=\"\r\n                  item.children && item.children.length > 0 && item.isExpanded\r\n                \"\r\n                class=\"history-children\"\r\n              >\r\n                <div\r\n                  v-for=\"(child, childIndex) in item.children\"\r\n                  :key=\"childIndex\"\r\n                  class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\"\r\n                >\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">\r\n                    {{ formatHistoryTime(child.createTime) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card\r\n                v-for=\"(ai, index) in aiList\"\r\n                :key=\"index\"\r\n                class=\"ai-card\"\r\n                shadow=\"hover\"\r\n              >\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\" />\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch\r\n                      v-model=\"ai.enabled\"\r\n                      active-color=\"#13ce66\"\r\n                      inactive-color=\"#ff4949\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <!-- 通义只支持单选-->\r\n                  <div v-if=\"ai.name === '通义千问'\" class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapability === capability.value ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"ai.selectedCapability !== capability.value\"\r\n                      @click=\"selectSingleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                  <!-- 其他AI -->\r\n                  <div v-else class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\"\r\n                      size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入提示词，支持Markdown格式\"\r\n              v-model=\"promptInput\"\r\n              resize=\"none\"\r\n              class=\"prompt-input\"\r\n            >\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"sendPrompt\"\r\n                :disabled=\"!canSend\"\r\n                class=\"send-button\"\r\n              >\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div\r\n                  v-for=\"(ai, index) in enabledAIs\"\r\n                  :key=\"index\"\r\n                  class=\"task-item\"\r\n                >\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i\r\n                        :class=\"[\r\n                          'el-icon-arrow-right',\r\n                          { 'is-expanded': ai.isExpanded },\r\n                        ]\"\r\n                      ></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{\r\n                        getStatusText(ai.status)\r\n                      }}</span>\r\n                      <i\r\n                        :class=\"getStatusIcon(ai.status)\"\r\n                        class=\"status-icon\"\r\n                      ></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div\r\n                    class=\"progress-timeline\"\r\n                    v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\"\r\n                  >\r\n                    <div class=\"timeline-scroll\">\r\n                      <div\r\n                        v-for=\"(log, logIndex) in ai.progressLogs\"\r\n                        :key=\"logIndex\"\r\n                        class=\"progress-item\"\r\n                        :class=\"{\r\n                          completed: log.isCompleted || logIndex > 0,\r\n                          current: !log.isCompleted && logIndex === 0,\r\n                        }\"\r\n                      >\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div\r\n                          class=\"progress-line\"\r\n                          v-if=\"logIndex < ai.progressLogs.length - 1\"\r\n                        ></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">\r\n                            {{ formatTime(log.timestamp) }}\r\n                          </div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch\r\n                    v-model=\"autoPlay\"\r\n                    active-text=\"自动轮播\"\r\n                    inactive-text=\"手动切换\"\r\n                  >\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel\r\n                  :interval=\"3000\"\r\n                  :autoplay=\"false\"\r\n                  indicator-position=\"outside\"\r\n                  height=\"700px\"\r\n                >\r\n                  <el-carousel-item\r\n                    v-for=\"(screenshot, index) in screenshots\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <img\r\n                      :src=\"screenshot\"\r\n                      alt=\"执行截图\"\r\n                      class=\"screenshot-image\"\r\n                      @click=\"showLargeImage(screenshot)\"\r\n                    />\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane\r\n            v-for=\"(result, index) in results\"\r\n            :key=\"index\"\r\n            :label=\"result.aiName\"\r\n            :name=\"'result-' + index\"\r\n          >\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-link\"\r\n                    @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\"\r\n                  >\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"success\"\r\n                    icon=\"el-icon-s-promotion\"\r\n                    @click=\"handlePushToMedia(result)\"\r\n                    class=\"push-media-btn\"\r\n                    :loading=\"pushingToMedia\"\r\n                    :disabled=\"pushingToMedia\"\r\n                  >\r\n                    投递到媒体\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img\r\n                  v-if=\"isImageFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  alt=\"分享图片\"\r\n                  class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\"\r\n                />\r\n                <!-- 渲染PDF -->\r\n                <iframe\r\n                  v-else-if=\"isPdfFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  class=\"share-pdf\"\r\n                  frameborder=\"0\"\r\n                >\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"openShareUrl(result.shareImgUrl)\"\r\n                  >\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"markdown-content\"\r\n                v-html=\"renderMarkdown(result.content)\"\r\n              ></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  @click=\"copyResult(result.content)\"\r\n                  >复制（纯文本）</el-button\r\n                >\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"success\"\r\n                  @click=\"exportResult(result)\"\r\n                  >导出（MD文件）</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"showImageDialog\"\r\n      width=\"90%\"\r\n      :show-close=\"true\"\r\n      :modal=\"true\"\r\n      center\r\n      class=\"image-dialog\"\r\n      :append-to-body=\"true\"\r\n      @close=\"closeLargeImage\"\r\n    >\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div\r\n          v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\"\r\n          class=\"single-image-container\"\r\n        >\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\" />\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel\r\n          v-else\r\n          :interval=\"3000\"\r\n          :autoplay=\"false\"\r\n          indicator-position=\"outside\"\r\n          height=\"80vh\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(screenshot, index) in screenshots\"\r\n            :key=\"index\"\r\n          >\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\" />\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog\r\n      title=\"智能评分\"\r\n      :visible.sync=\"scoreDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\"\r\n    >\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"10\"\r\n            placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\"\r\n            v-model=\"scorePrompt\"\r\n            resize=\"none\"\r\n            class=\"score-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox\r\n              v-for=\"(result, index) in results\"\r\n              :key=\"index\"\r\n              :label=\"result.aiName\"\r\n              class=\"result-checkbox\"\r\n            >\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 投递到媒体弹窗 -->\r\n    <el-dialog\r\n      title=\"媒体投递设置\"\r\n      :visible.sync=\"layoutDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"layout-dialog\"\r\n    >\r\n      <div class=\"layout-dialog-content\">\r\n        <!-- 媒体选择区域 -->\r\n        <div class=\"media-selection-section\">\r\n          <h3>选择投递媒体：</h3>\r\n          <el-radio-group v-model=\"selectedMedia\" size=\"small\" class=\"media-radio-group\">\r\n            <el-radio-button label=\"wechat\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              公众号\r\n            </el-radio-button>\r\n            <el-radio-button label=\"zhihu\">\r\n              <i class=\"el-icon-document\"></i>\r\n              知乎\r\n            </el-radio-button>\r\n            <el-radio-button label=\"toutiao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              微头条\r\n            </el-radio-button>\r\n            <el-radio-button label=\"baijiahao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              百家号\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n          <div class=\"media-description\">\r\n            <template v-if=\"selectedMedia === 'wechat'\">\r\n              <small>📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'zhihu'\">\r\n              <small>📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>📰 将内容转换为微头条文章格式，支持文章编辑和发布</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>🔈 将内容转换为百家号帖子格式，直接投递到百家号草稿箱</small>\r\n            </template>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"layout-prompt-section\">\r\n          <h3>排版提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"12\"\r\n            placeholder=\"请输入排版提示词\"\r\n            v-model=\"layoutPrompt\"\r\n            resize=\"none\"\r\n            class=\"layout-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"layoutDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleLayout\" :disabled=\"!canLayout\">\r\n          排版后智能投递\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条发布流程弹窗 -->\r\n    <el-dialog title=\"微头条发布流程\" :visible.sync=\"tthFlowVisible\" width=\"60%\" height=\"60%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-flow-dialog\">\r\n      <div class=\"tth-flow-content\">\r\n        <div class=\"flow-logs-section\">\r\n          <h3>发布流程日志：</h3>\r\n          <div class=\"progress-timeline\">\r\n            <div class=\"timeline-scroll\">\r\n              <div v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" class=\"progress-item completed\">\r\n                <div class=\"progress-dot\"></div>\r\n                <div v-if=\"index < tthFlowLogs.length - 1\" class=\"progress-line\"></div>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                  <div class=\"progress-text\">{{ log.content }}</div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"tthFlowLogs.length === 0\" class=\"no-logs\">暂无流程日志...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flow-images-section\">\r\n          <h3>发布流程图片：</h3>\r\n          <div class=\"flow-images-container\">\r\n            <template v-if=\"tthFlowImages.length > 0\">\r\n              <div v-for=\"(image, index) in tthFlowImages\" :key=\"index\" class=\"flow-image-item\">\r\n                <img :src=\"image\" alt=\"流程图片\" class=\"flow-image\" @click=\"showLargeImage(image)\">\r\n              </div>\r\n            </template>\r\n            <div v-else class=\"no-logs\">暂无流程图片...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeTTHFlowDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条文章编辑弹窗 -->\r\n    <el-dialog title=\"微头条文章编辑\" :visible.sync=\"tthArticleEditVisible\" width=\"70%\" height=\"80%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-article-edit-dialog\">\r\n      <div class=\"tth-article-edit-content\">\r\n        <div class=\"article-title-section\">\r\n          <h3>文章标题：</h3>\r\n          <el-input v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" class=\"article-title-input\"></el-input>\r\n        </div>\r\n        <div class=\"article-content-section\">\r\n          <h3>文章内容：</h3>\r\n          <div class=\"content-input-wrapper\">\r\n            <el-input \r\n              type=\"textarea\" \r\n              v-model=\"tthArticleContent\" \r\n              :rows=\"20\" \r\n              placeholder=\"请输入文章内容\"\r\n              resize=\"none\" \r\n              class=\"article-content-input\"\r\n              :class=\"{ 'content-over-limit': tthArticleContent.length > 2000 }\"\r\n            ></el-input>\r\n            <div class=\"content-length-info\" :class=\"{ 'text-danger': tthArticleContent.length > 2000 }\">\r\n              字数：{{ tthArticleContent.length }}/2000\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"tthArticleEditVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmTTHPublish\" :disabled=\"!tthArticleTitle || !tthArticleContent\">\r\n          确定发布\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { marked } from \"marked\";\r\nimport {\r\n  message,\r\n  saveUserChatData,\r\n  getChatHistory,\r\n  pushAutoOffice,\r\n  getMediaCallWord,\r\n} from \"@/api/wechat/aigc\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport websocketClient from \"@/utils/websocket\";\r\nimport store from \"@/store\";\r\nimport TurndownService from \"turndown\";\r\n\r\nexport default {\r\n  name: \"AIManagementPlatform\",\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: \"\",\r\n        userId: \"\",\r\n        corpId: \"\",\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        isNewChat: true,\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"\",\r\n        params: {},\r\n      },\r\n      aiList: [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: \"\",\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: \"result-0\",\r\n      activeCollapses: [\"ai-selection\", \"prompt-input\"], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: \"\",\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: \"atx\",\r\n        codeBlockStyle: \"fenced\",\r\n        emDelimiter: \"*\",\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇内容，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      layoutDialogVisible: false,\r\n      layoutPrompt: \"\",\r\n      currentLayoutResult: null, // 当前要排版的结果\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n      selectedMedia: \"wechat\", // 默认选择公众号\r\n      pushingToMedia: false, // 投递到媒体的loading状态\r\n      // 微头条相关变量\r\n      tthFlowVisible: false, // 微头条发布流程弹窗\r\n      tthFlowLogs: [], // 微头条发布流程日志\r\n      tthFlowImages: [], // 微头条发布流程图片\r\n      tthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n      tthArticleTitle: '', // 微头条文章标题\r\n      tthArticleContent: '', // 微头条文章内容\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return (\r\n        this.promptInput.trim().length > 0 &&\r\n        this.aiList.some((ai) => ai.enabled)\r\n      );\r\n    },\r\n    canScore() {\r\n      return (\r\n        this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0\r\n      );\r\n    },\r\n    canLayout() {\r\n      return this.layoutPrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach((item) => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach((chatGroup) => {\r\n        // 按时间排序\r\n        chatGroup.sort(\r\n          (a, b) => new Date(a.createTime) - new Date(b.createTime)\r\n        );\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map((child) => ({\r\n            ...child,\r\n            isParent: false,\r\n          })),\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    },\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  watch: {\r\n    // 监听媒体选择变化，自动加载对应的提示词\r\n    selectedMedia: {\r\n      handler(newMedia) {\r\n        this.loadMediaPrompt(newMedia);\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots = [];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = \"\";\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter((ai) => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach((ai) => {\r\n        this.$set(ai, \"status\", \"running\");\r\n      });\r\n\r\n      this.enabledAIs.forEach((ai) => {\r\n        if (ai.name === \"DeepSeek\" && ai.enabled) {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"deepseek,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-lwss,\";\r\n          }\r\n        }\r\n        if (ai.name === \"豆包\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db-sdsk,\";\r\n          }\r\n        }\r\n        if (ai.name === \"MiniMax Chat\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n          }\r\n        }\r\n        if(ai.name === '通义千问' && ai.enabled){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n          if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n          } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq);\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\";\r\n      this.jsonRpcReqest.params = this.userInfoReq;\r\n      this.message(this.jsonRpcReqest);\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then((res) => {\r\n        if (res.code == 201) {\r\n          this.$message.error(res.messages || '操作失败');\r\n        }\r\n      });\r\n    },\r\n    // 处理通义单选逻辑\r\n    selectSingleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      if (ai.selectedCapability === capabilityValue) {\r\n        this.$set(ai, 'selectedCapability', '');\r\n      } else {\r\n        this.$set(ai, 'selectedCapability', capabilityValue);\r\n      }\r\n      this.$forceUpdate();\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log(\"切换前:\", ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(\r\n          ai.selectedCapabilities,\r\n          ai.selectedCapabilities.length,\r\n          capabilityValue\r\n        );\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, \"selectedCapabilities\", newCapabilities);\r\n      }\r\n      console.log(\"切换后:\", ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"等待中\";\r\n        case \"running\":\r\n          return \"正在执行\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"failed\":\r\n          return \"执行失败\";\r\n        default:\r\n          return \"未知状态\";\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"el-icon-time\";\r\n        case \"running\":\r\n          return \"el-icon-loading\";\r\n        case \"completed\":\r\n          return \"el-icon-check success-icon\";\r\n        case \"failed\":\r\n          return \"el-icon-close error-icon\";\r\n        default:\r\n          return \"el-icon-question\";\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement(\"div\");\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || \"\";\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement(\"textarea\");\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textarea);\r\n      this.$message.success(\"已复制纯文本到剪贴板\");\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: \"text/markdown\" });\r\n      const link = document.createElement(\"a\");\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date()\r\n        .toISOString()\r\n        .slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success(\"已导出Markdown文件\");\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, \"_blank\");\r\n      } else {\r\n        this.$message.warning(\"暂无原链接\");\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector(\".image-dialog .el-carousel\");\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = \"\";\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log(\"WebSocket URL:\", process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case \"open\":\r\n            // this.$message.success('');\r\n            break;\r\n          case \"message\":\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case \"close\":\r\n            this.$message.warning(\"WebSocket连接已关闭\");\r\n            break;\r\n          case \"error\":\r\n            this.$message.error(\"WebSocket连接错误\");\r\n            break;\r\n          case \"reconnect_failed\":\r\n            this.$message.error(\"WebSocket重连失败，请刷新页面重试\");\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === \"RETURN_YBT1_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_YBDS_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_DB_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.tyChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.maxChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_LOG\" && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(\r\n          (ai) => ai.name === dataObj.aiName\r\n        );\r\n        if (targetAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = targetAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            targetAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到知乎\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            zhihuAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到百家号\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            baijiahaoAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理截图消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_IMG\" && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n      // 处理智能评分结果\r\n      if (dataObj.type === \"RETURN_WKPF_RES\") {\r\n        const wkpfAI = this.enabledAIs.find((ai) => ai.name === \"智能评分\");\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, \"status\", \"completed\");\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: \"智能评分\",\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能排版结果\r\n      if (dataObj.type === \"RETURN_ZNPB_RES\") {\r\n        const znpbAI = this.enabledAIs.find((ai) => ai.name === \"智能排版\");\r\n        if (znpbAI) {\r\n          this.$set(znpbAI, \"status\", \"completed\");\r\n          if (znpbAI.progressLogs.length > 0) {\r\n            this.$set(znpbAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 直接调用投递到公众号的方法，不添加到结果展示\r\n          this.pushToWechatWithContent(dataObj.draftContent);\r\n\r\n          // 智能排版完成时，保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_ZHIHU_DELIVERY_RES\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          this.$set(zhihuAI, \"status\", \"completed\");\r\n          if (zhihuAI.progressLogs.length > 0) {\r\n            this.$set(zhihuAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          zhihuAI.progressLogs.unshift({\r\n            content: \"知乎投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 知乎投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"知乎投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_BAIJIAHAO_DELIVERY_RES\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          this.$set(baijiahaoAI, \"status\", \"completed\");\r\n          if (baijiahaoAI.progressLogs.length > 0) {\r\n            this.$set(baijiahaoAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          baijiahaoAI.progressLogs.unshift({\r\n            content: \"百家号投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 百家号投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"百家号投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理微头条排版结果\r\n      if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n        // 微头条排版AI节点状态设为已完成\r\n        const tthpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n        if (tthpbAI) {\r\n          this.$set(tthpbAI, 'status', 'completed');\r\n          if (tthpbAI.progressLogs.length > 0) {\r\n            this.$set(tthpbAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n        }\r\n        this.tthArticleTitle = dataObj.title || '';\r\n        this.tthArticleContent = dataObj.content || '';\r\n        this.tthArticleEditVisible = true;\r\n        this.saveHistory();\r\n        return;\r\n      }\r\n\r\n      // 处理微头条发布流程\r\n      if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n        // 添加流程日志\r\n        if (dataObj.content) {\r\n          this.tthFlowLogs.push({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            type: 'flow',\r\n          });\r\n        }\r\n        // 处理图片信息\r\n        if (dataObj.shareImgUrl) {\r\n          this.tthFlowImages.push(dataObj.shareImgUrl);\r\n        }\r\n        // 确保流程弹窗显示\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        // 检查发布结果\r\n        if (dataObj.content === 'success') {\r\n          this.$message.success('发布到微头条成功！');\r\n          this.tthFlowVisible = true;\r\n        } else if (dataObj.content === 'false' || dataObj.content === false) {\r\n          this.$message.error('发布到微头条失败！');\r\n          this.tthFlowVisible = false;\r\n          this.tthArticleEditVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 兼容后端发送的RETURN_PC_TTH_IMG类型图片消息\r\n      if (dataObj.type === 'RETURN_PC_TTH_IMG' && dataObj.url) {\r\n        this.tthFlowImages.push(dataObj.url);\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case \"RETURN_YBT1_RES\":\r\n        case \"RETURN_TURBOS_RES\":\r\n        case \"RETURN_TURBOS_LARGE_RES\":\r\n        case \"RETURN_DEEPSEEK_RES\":\r\n          console.log(\"收到DeepSeek消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"DeepSeek\");\r\n          break;\r\n        case \"RETURN_YBDS_RES\":\r\n        case \"RETURN_DB_RES\":\r\n          console.log(\"收到豆包消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"豆包\");\r\n          break;\r\n        case \"RETURN_MAX_RES\":\r\n          console.log(\"收到MiniMax消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n          break;\r\n        case 'RETURN_TY_RES':\r\n          console.log('收到通义千问消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n          break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, \"status\", \"completed\");\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], \"isCompleted\", true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(\r\n          (r) => r.aiName === targetAI.name\r\n        );\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error(\"WebSocket未连接\");\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, \"isExpanded\", !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter((result) => this.selectedResults.includes(result.aiName))\r\n        .map((result) => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join(\"\\n\");\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"AI评分\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: \"zj-db-sdsk\", // 默认使用豆包进行评分\r\n        },\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest);\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: \"智能评分\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"智能评分任务已提交，正在评分...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"智能评分\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"智能评分\"\r\n      );\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"评分请求已发送，请等待结果\");\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return \"今天\";\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return \"昨天\";\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return \"两天前\";\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return \"三天前\";\r\n      } else {\r\n        return date.toLocaleDateString(\"zh-CN\", {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || \"\";\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || \"\";\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || \"\";\r\n        this.userInfoReq.dbChatId = item.dbChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.tyChatId || \"\";\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success(\"历史记录加载成功\");\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId,\r\n        tyChatId: this.userInfoReq.tyChatId,\r\n        maxChatId: this.userInfoReq.maxChatId,\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n          maxChatId: this.userInfoReq.maxChatId,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"保存历史记录失败:\", error);\r\n        this.$message.error(\"保存历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(\r\n        this.expandedHistoryItems,\r\n        item.chatId,\r\n        !this.expandedHistoryItems[item.chatId]\r\n      );\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = \"\";\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: \"\",\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        maxChatId: \"\",\r\n        isNewChat: true,\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n\r\n      this.$message.success(\"已创建新对话\");\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId, 0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载上次会话失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = [\r\n        \".jpg\",\r\n        \".jpeg\",\r\n        \".png\",\r\n        \".gif\",\r\n        \".bmp\",\r\n        \".webp\",\r\n        \".svg\",\r\n      ];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some((ext) => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes(\".pdf\");\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        DeepSeek: \"700px\",\r\n        豆包: \"560px\",\r\n        通义千问: \"700px\",\r\n      };\r\n\r\n      const width = widthMap[aiName] || \"560px\"; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: \"auto\",\r\n      };\r\n    },\r\n\r\n    // 投递到媒体\r\n    handlePushToMedia(result) {\r\n      this.currentLayoutResult = result;\r\n      this.showLayoutDialog(result);\r\n    },\r\n\r\n    // 显示智能排版对话框\r\n    showLayoutDialog(result) {\r\n      this.currentLayoutResult = result;\r\n      this.layoutDialogVisible = true;\r\n      // 加载当前选择媒体的提示词\r\n      this.loadMediaPrompt(this.selectedMedia);\r\n    },\r\n\r\n    // 加载媒体提示词\r\n    async loadMediaPrompt(media) {\r\n      if (!media) return;\r\n\r\n      let platformId;\r\n      if(media === 'wechat'){\r\n        platformId = 'wechat_layout';\r\n      }else if(media === 'zhihu'){\r\n        platformId = 'zhihu_layout';\r\n      }else if(media === 'baijiahao'){\r\n        platformId = 'baijiahao_layout';\r\n      }else if(media === 'toutiao'){\r\n        platformId = 'weitoutiao_layout';\r\n      }\r\n\r\n      try {\r\n        const response = await getMediaCallWord(platformId);\r\n        if (response.code === 200) {\r\n          this.layoutPrompt = response.data + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        } else {\r\n          // 使用默认提示词\r\n          this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载提示词失败:', error);\r\n        // 使用默认提示词\r\n        this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n      }\r\n    },\r\n\r\n    // 获取默认提示词(仅在后端访问失败时使用)\r\n    getDefaultPrompt(media) {\r\n      if (media === 'wechat') {\r\n        return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：`;\r\n      } else if (media === 'zhihu') {\r\n        return `请将以下内容整理为适合知乎发布的Markdown格式文章。要求：\r\n1. 保持内容的专业性和可读性\r\n2. 使用合适的标题层级（## ### #### 等）\r\n3. 代码块使用\\`\\`\\`标记，并指定语言类型\r\n4. 重要信息使用**加粗**标记\r\n5. 列表使用- 或1. 格式\r\n6. 删除不必要的格式标记\r\n7. 确保内容适合知乎的阅读习惯\r\n8. 文章结构清晰，逻辑连贯\r\n9. 目标是作为一篇专业文章投递到知乎草稿箱\r\n\r\n请对以下内容进行排版：`;\r\n\r\n      }else if (media === 'baijiahao') {\r\n        return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括\"一、\"，\"1.\"等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话`;\r\n\r\n      }else if (media === 'toutiao') {\r\n        return `根据智能评分内容，写一篇微头条文章，只能包含标题和内容，要求如下：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数严格控制在1000字以上，2000字以下\r\n7. 强制要求：只能回答标题和内容，标题必须用英文双引号（\"\"）引用起来，且放在首位，不能有其他多余的话\r\n8. 严格要求：AI必须严格遵守所有严格条件，不要输出其他多余的内容，只要标题和内容\r\n9. 内容不允许出现编号，要正常文章格式\r\n\r\n请对以下内容进行排版：`;\r\n      }\r\n      return '请对以下内容进行排版：';\r\n    },\r\n\r\n    // 处理智能排版\r\n    handleLayout() {\r\n      if (!this.canLayout || !this.currentLayoutResult) return;\r\n      this.layoutDialogVisible = false;\r\n\r\n      if (this.selectedMedia === 'zhihu') {\r\n        // 知乎投递：直接创建投递任务\r\n        this.createZhihuDeliveryTask();\r\n      } else if (this.selectedMedia === 'toutiao') {\r\n        // 微头条投递：创建微头条排版任务\r\n        this.createToutiaoLayoutTask();\r\n      } else if (this.selectedMedia === 'baijiahao') {\r\n        // 百家号投递：创建百家号排版任务\r\n        this.createBaijiahaoLayoutTask();\r\n      }else {\r\n        // 公众号投递：创建排版任务\r\n        this.createWechatLayoutTask();\r\n      }\r\n    },\r\n// 创建知乎投递任务（独立任务）\r\n    createZhihuDeliveryTask() {\r\n      const zhihuAI = {\r\n        name: \"投递到知乎\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"知乎投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到知乎\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在知乎投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到知乎\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(zhihuAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = zhihuAI;\r\n        const zhihu = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(zhihu);\r\n      }\r\n\r\n      // 发送知乎投递请求\r\n      const zhihuRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到知乎\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"zhihu\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"知乎投递参数\", zhihuRequest);\r\n      this.message(zhihuRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"知乎投递任务已创建，正在处理...\");\r\n    },\r\n    // 创建百家号投递任务（独立任务）\r\n    createBaijiahaoLayoutTask() {\r\n      const baijiahaoAI = {\r\n        name: \"投递到百家号\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"百家号投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到百家号\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在百家号投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到百家号\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = baijiahaoAI;\r\n        const baijiahao = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      }\r\n\r\n      // 发送百家号投递请求\r\n      const baijiahaoRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到百家号\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"baijiahao\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"百家号投递参数\", baijiahaoRequest);\r\n      this.message(baijiahaoRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"百家号投递任务已创建，正在处理...\");\r\n    },\r\n      // 创建公众号排版任务（保持原有逻辑）\r\n      createWechatLayoutTask() {\r\n        const layoutRequest = {\r\n          jsonrpc: \"2.0\",\r\n          id: uuidv4(),\r\n          method: \"AI排版\",\r\n          params: {\r\n            taskId: uuidv4(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: this.layoutPrompt,\r\n            roles: \"\",\r\n            selectedMedia: \"wechat\",\r\n          },\r\n        };\r\n\r\n        console.log(\"公众号排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        const znpbAI = {\r\n          name: \"智能排版\",\r\n          avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"running\",\r\n          progressLogs: [\r\n            {\r\n              content: \"智能排版任务已提交，正在排版...\",\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: \"智能排版\",\r\n            },\r\n          ],\r\n          isExpanded: true,\r\n        };\r\n\r\n        // 检查是否已存在智能排版任务\r\n        const existIndex = this.enabledAIs.findIndex(\r\n          (ai) => ai.name === \"智能排版\"\r\n        );\r\n        if (existIndex === -1) {\r\n          this.enabledAIs.unshift(znpbAI);\r\n        } else {\r\n          this.enabledAIs[existIndex] = znpbAI;\r\n          const znpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n          this.enabledAIs.unshift(znpb);\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.$message.success(\"排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 创建微头条排版任务\r\n    createToutiaoLayoutTask() {\r\n      // 获取智能评分内容\r\n      const scoreResult = this.results.find(r => r.aiName === '智能评分');\r\n      const scoreContent = scoreResult ? scoreResult.content : '';\r\n\r\n      const layoutRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"微头条排版\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: `${scoreContent}\\n${this.layoutPrompt}`,\r\n          roles: \"\",\r\n        },\r\n      };\r\n\r\n      console.log(\"微头条排版参数\", layoutRequest);\r\n      this.message(layoutRequest);\r\n\r\n      const tthpbAI = {\r\n        name: \"微头条排版\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"微头条排版任务已提交，正在排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"微头条排版\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在微头条排版任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"微头条排版\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(tthpbAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = tthpbAI;\r\n        const tthpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(tthpb);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"微头条排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 实际投递到公众号\r\n    pushToWechatWithContent(contentText) {\r\n      if (this.pushingToWechat) return;\r\n      this.$message.success(\"开始投递公众号！\");\r\n      this.pushingToWechat = true;\r\n      this.pushOfficeNum += 1;\r\n\r\n      const params = {\r\n        contentText: contentText,\r\n        shareUrl: this.currentLayoutResult.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: this.currentLayoutResult.aiName,\r\n      };\r\n\r\n      pushAutoOffice(params)\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.$message.success(\"投递到公众号成功！\");\r\n          } else {\r\n            this.$message.error(res.msg || \"投递失败，请重试\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"投递到公众号失败:\", error);\r\n          this.$message.error(\"投递失败，请重试\");\r\n        })\r\n        .finally(() => {\r\n          this.pushingToWechat = false;\r\n        });\r\n    },\r\n\r\n\r\n\r\n    // 确认微头条发布\r\n    confirmTTHPublish() {\r\n      if (!this.tthArticleTitle || !this.tthArticleContent) {\r\n        this.$message.warning('请填写标题和内容');\r\n        return;\r\n      }\r\n      // 构建微头条发布请求\r\n      const publishRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n                  method: '微头条发布',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          roles: '',\r\n          title: this.tthArticleTitle,\r\n          content: this.tthArticleContent,\r\n          type: '微头条发布'\r\n        }\r\n      };\r\n      // 发送发布请求\r\n      console.log(\"微头条发布参数\", publishRequest);\r\n      this.message(publishRequest);\r\n      this.tthArticleEditVisible = false;\r\n      // 显示流程弹窗\r\n      this.tthFlowVisible = true;\r\n      this.tthFlowLogs = [];\r\n      this.tthFlowImages = [];\r\n      this.$message.success('微头条发布请求已发送！');\r\n    },\r\n\r\n\r\n    // 关闭微头条发布流程弹窗\r\n    closeTTHFlowDialog() {\r\n      this.tthFlowVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card,\r\n.screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn,\r\n.push-media-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.layout-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.layout-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.layout-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.layout-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.layout-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.layout-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409eff;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 用于处理DeepSeek特殊格式的样式 */\r\n.deepseek-format-container {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 5px;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n/* DeepSeek响应内容的特定样式 */\r\n::v-deep .deepseek-response {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n::v-deep .deepseek-response pre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  display: block;\r\n  margin: 10px 0;\r\n}\r\n\r\n::v-deep .deepseek-response code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 4px;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n::v-deep .deepseek-response table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 15px 0;\r\n}\r\n\r\n::v-deep .deepseek-response th,\r\n::v-deep .deepseek-response td {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n::v-deep .deepseek-response th {\r\n  background-color: #f2f2f2;\r\n  font-weight: bold;\r\n}\r\n\r\n::v-deep .deepseek-response h1,\r\n::v-deep .deepseek-response h2,\r\n::v-deep .deepseek-response h3,\r\n::v-deep .deepseek-response h4,\r\n::v-deep .deepseek-response h5,\r\n::v-deep .deepseek-response h6 {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n  color: #222;\r\n}\r\n\r\n::v-deep .deepseek-response a {\r\n  color: #0066cc;\r\n  text-decoration: none;\r\n}\r\n\r\n::v-deep .deepseek-response blockquote {\r\n  border-left: 4px solid #ddd;\r\n  padding-left: 15px;\r\n  margin: 15px 0;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .deepseek-response ul,\r\n::v-deep .deepseek-response ol {\r\n  padding-left: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 媒体选择区域样式 */\r\n.media-selection-section {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.media-selection-section h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.media-radio-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner {\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner i {\r\n  font-size: 14px;\r\n}\r\n\r\n.media-description {\r\n  margin-top: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.media-description small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.layout-prompt-section h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 微头条相关样式 */\r\n.tth-flow-dialog {\r\n  .tth-flow-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 600px;\r\n  }\r\n\r\n  .flow-logs-section,\r\n  .flow-images-section {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .flow-logs-section h3,\r\n  .flow-images-section h3 {\r\n    margin: 0 0 12px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-timeline {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    padding: 12px;\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  .timeline-scroll {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .progress-item {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .progress-dot {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 4px;\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #67c23a;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .progress-line {\r\n    position: absolute;\r\n    left: 3px;\r\n    top: 12px;\r\n    width: 2px;\r\n    height: 20px;\r\n    background-color: #e4e7ed;\r\n  }\r\n\r\n  .progress-content {\r\n    .progress-time {\r\n      font-size: 12px;\r\n      color: #909399;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 13px;\r\n      color: #303133;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .flow-images-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n    max-height: 500px;\r\n  }\r\n\r\n  .flow-image-item {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .flow-image-item:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .flow-image {\r\n    max-width: 100%;\r\n    max-height: 400px;\r\n    min-height: 200px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    border: 2px solid #e4e7ed;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    object-fit: contain;\r\n  }\r\n\r\n  .flow-image:hover {\r\n    transform: scale(1.02);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    border-color: #409eff;\r\n  }\r\n\r\n  .no-logs {\r\n    text-align: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.tth-article-edit-dialog {\r\n  .tth-article-edit-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .article-title-section h3,\r\n  .article-content-section h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .article-title-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .article-content-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .content-input-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .content-length-info {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    z-index: 1;\r\n  }\r\n\r\n  .text-danger {\r\n    color: #f56c6c !important;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content-over-limit .el-textarea__inner {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}